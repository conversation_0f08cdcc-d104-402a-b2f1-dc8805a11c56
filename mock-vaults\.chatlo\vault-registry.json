{"version": "1.0", "vaultRoot": "mock-vaults", "vaults": [{"id": "work-vault-001", "name": "Work Vault", "path": "mock-vaults/work-vault", "description": "Work-related documents and projects", "color": "#FF8383", "icon": "fa-briefcase", "contexts": [{"id": "client-project-001", "name": "Client Project", "path": "mock-vaults/work-vault/client-project", "description": "Client project documentation and files", "color": "#FF8383", "icon": "fa-folder", "status": "active", "stats": {"fileCount": 2, "conversationCount": 0, "lastModified": "2025-01-15T20:35:00.000Z", "sizeBytes": 2048}, "masterDoc": null, "aiInsights": {"suggestedActions": ["Review documents", "Start conversation"], "contextType": "project", "readinessScore": 0.2}}]}, {"id": "personal-vault-001", "name": "Personal Vault", "path": "mock-vaults/personal-vault", "description": "Personal research and notes", "color": "#8AB0BB", "icon": "fa-user", "contexts": [{"id": "research-notes-001", "name": "Research Notes", "path": "mock-vaults/personal-vault/research-notes", "description": "AI and technology research notes", "color": "#8AB0BB", "icon": "fa-folder", "status": "active", "stats": {"fileCount": 1, "conversationCount": 0, "lastModified": "2025-01-15T20:35:00.000Z", "sizeBytes": 1024}, "masterDoc": null, "aiInsights": {"suggestedActions": ["Review research", "Add more notes"], "contextType": "research", "readinessScore": 0.1}}]}], "lastScan": "2025-01-15T20:35:00.000Z", "preferences": {"defaultVault": "work-vault-001", "defaultContext": "client-project-001", "autoOrganize": true, "showEmptyHints": true}}