/**
 * File Intelligence Processing Types
 * Core interfaces for intelligent document analysis and context vault organization
 */

// Intent types that can co-exist in documents
export type IntentType = 'topic' | 'knowledge' | 'connection' | 'action' | 'reference'

// Priority levels for weighted entity extraction
export type PriorityLevel = 'high' | 'medium' | 'low'

// Entity types with priority mapping
export type EntityType = 
  | 'person'           // High priority
  | 'email'            // High priority  
  | 'company'          // High priority
  | 'title'            // High priority
  | 'technical_concept' // Medium priority
  | 'project'          // Medium priority
  | 'date'             // Medium priority
  | 'location'         // Medium priority
  | 'keyword'          // Low priority
  | 'reference'        // Low priority
  | 'general'          // Low priority

// Key idea extracted from document with relevance scoring
export interface KeyIdea {
  id: string
  text: string
  relevance_score: number // 0-100
  intent_types: IntentType[]
  weight: number // 0.0-1.0 based on priority
  auto_selected: boolean // Top 3 auto-selected
  user_confirmed: boolean // User manually selected
  context?: string
  extracted_from?: string // Section or page reference
}

// Weighted entity with priority classification
export interface WeightedEntity {
  text: string
  type: EntityType
  confidence: number // 0.0-1.0
  weight: number // 0.0-1.0 based on priority level
  intent_types: IntentType[]
  context: string
  priority_level: PriorityLevel
  // Human connection specific fields
  email?: string
  company?: string
  title?: string
  role?: string
}

// User annotation for custom insights
export interface UserAnnotation {
  text: string
  timestamp: string
  relevance_score: number
  intent_types: IntentType[]
  user_id?: string
}

// Human connection extracted from document
export interface HumanConnection {
  name: string
  email?: string
  company?: string
  title?: string
  role?: string
  connection_strength: number // 0.0-1.0
  collaboration_context: string
  document_mentions: number
  priority_weight: number // Always 1.0 for human connections
}

// File-level intelligence analysis result (simplified for compatibility)
export interface FileIntelligence {
  file_path: string
  key_ideas: KeyIdea[]
  weighted_entities: WeightedEntity[]
  human_connections: HumanConnection[]
  processing_confidence: number
  analysis_metadata: {
    processing_time_ms: number
    model_used: string
    timestamp: string
  }
  created_at: string
  updated_at: string
}

// Vault-level intelligence aggregation (simplified for compatibility)
export interface VaultIntelligence {
  vault_path: string
  total_files_processed: number
  last_updated: string
  aggregated_ideas: KeyIdea[]
  top_entities: WeightedEntity[]
  human_connections: HumanConnection[]
  processing_summary: {
    total_processing_time_ms: number
    average_confidence: number
    models_used: string[]
    last_full_scan: string
  }
}

// Processing configuration
export interface FileIntelligenceConfig {
  local_model_preferred: string // e.g., 'ollama:gemma2-9b-instruct'
  min_ideas_required: number // Minimum 10
  confidence_threshold: number // 0.0-1.0
  auto_select_top_n: number // Default 3
  enable_human_priority: boolean // Always prioritize people/companies
  enable_user_annotations: boolean
  processing_timeout_ms: number
  fallback_to_keyword_extraction: boolean
}

// Processing progress tracking
export interface ProcessingProgress {
  file_path: string
  stage: 'extracting' | 'analyzing' | 'storing' | 'complete' | 'error'
  progress_percentage: number
  current_operation: string
  estimated_time_remaining?: number
  error_message?: string
}

// Processing result for UI feedback
export interface ProcessingResult {
  success: boolean
  file_path: string
  processing_time_ms: number
  ideas_extracted: number
  entities_found: number
  human_connections: number
  confidence_score: number
  error_message?: string
  local_model_used?: string
}

// Batch processing status
export interface BatchProcessingStatus {
  total_files: number
  processed_files: number
  failed_files: number
  current_file?: string
  overall_progress: number
  start_time: string
  estimated_completion?: string
  results: ProcessingResult[]
}

// Label selection UI state
export interface LabelSelectionState {
  available_ideas: KeyIdea[]
  selected_idea_ids: string[]
  user_annotation: string
  processing_status: 'idle' | 'processing' | 'complete' | 'error'
  show_all_ideas: boolean // Toggle to show beyond top suggestions
}

// Export default configuration
export const DEFAULT_FILE_INTELLIGENCE_CONFIG: FileIntelligenceConfig = {
  local_model_preferred: 'ollama:gemma2-9b-instruct',
  min_ideas_required: 10,
  confidence_threshold: 0.7,
  auto_select_top_n: 3,
  enable_human_priority: true,
  enable_user_annotations: true,
  processing_timeout_ms: 30000, // 30 seconds
  fallback_to_keyword_extraction: true
}

// Priority weight mapping
export const PRIORITY_WEIGHTS: Record<PriorityLevel, number> = {
  high: 1.0,    // People, emails, companies, titles
  medium: 0.7,  // Technical concepts, projects, dates
  low: 0.4      // General keywords, references
}

// Entity type to priority mapping
export const ENTITY_PRIORITY_MAP: Record<EntityType, PriorityLevel> = {
  person: 'high',
  email: 'high',
  company: 'high',
  title: 'high',
  technical_concept: 'medium',
  project: 'medium',
  date: 'medium',
  location: 'medium',
  keyword: 'low',
  reference: 'low',
  general: 'low'
}
