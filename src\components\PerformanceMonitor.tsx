import React, { useState, useEffect } from 'react'
import { Activity, Clock, HardDrive, Cpu } from './Icons'

interface PerformanceStats {
  imageProcessingTime: number
  ocrProcessingTime: number
  fileUploadTime: number
  fileIntelligenceTime: number
  memoryUsage: number
  activeProcesses: number
}

interface PerformanceMonitorProps {
  isVisible: boolean
  onToggle: () => void
}

const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({ isVisible, onToggle }) => {
  const [stats, setStats] = useState<PerformanceStats>({
    imageProcessingTime: 0,
    ocrProcessingTime: 0,
    fileUploadTime: 0,
    fileIntelligenceTime: 0,
    memoryUsage: 0,
    activeProcesses: 0
  })

  const [processingHistory, setProcessingHistory] = useState<Array<{
    timestamp: number
    type: 'image' | 'ocr' | 'upload' | 'intelligence'
    duration: number
    filename: string
    details?: string
  }>>([])

  useEffect(() => {
    // Listen for performance events from the main process
    const handlePerformanceUpdate = (event: any) => {
      const { type, duration, filename } = event.detail
      
      setStats(prev => ({
        ...prev,
        [`${type}ProcessingTime`]: duration
      }))

      setProcessingHistory(prev => [
        ...prev.slice(-9), // Keep last 10 entries
        {
          timestamp: Date.now(),
          type,
          duration,
          filename
        }
      ])
    }

    // Mock performance data for demo
    const interval = setInterval(() => {
      setStats(prev => ({
        ...prev,
        memoryUsage: Math.random() * 100,
        activeProcesses: Math.floor(Math.random() * 5)
      }))
    }, 2000)

    // Listen for file intelligence processing events
    const handleIntelligenceUpdate = (event: any) => {
      const { processingTime, fileName, ideasExtracted, entitiesFound } = event.detail

      setStats(prev => ({
        ...prev,
        fileIntelligenceTime: processingTime
      }))

      setProcessingHistory(prev => [
        ...prev.slice(-9), // Keep last 10 entries
        {
          timestamp: Date.now(),
          type: 'intelligence',
          duration: processingTime,
          filename: fileName,
          details: `${ideasExtracted} ideas, ${entitiesFound} entities`
        }
      ])
    }

    window.addEventListener('performance-update', handlePerformanceUpdate)
    window.addEventListener('file-intelligence-complete', handleIntelligenceUpdate)

    return () => {
      clearInterval(interval)
      window.removeEventListener('performance-update', handlePerformanceUpdate)
      window.removeEventListener('file-intelligence-complete', handleIntelligenceUpdate)
    }
  }, [])

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms.toFixed(0)}ms`
    return `${(ms / 1000).toFixed(1)}s`
  }

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString()
  }

  const getPerformanceColor = (duration: number, type: string) => {
    const thresholds = {
      image: { good: 2000, warning: 5000 },
      ocr: { good: 3000, warning: 8000 },
      upload: { good: 1000, warning: 3000 },
      intelligence: { good: 5000, warning: 15000 }
    }

    const threshold = thresholds[type as keyof typeof thresholds]
    if (duration < threshold.good) return 'text-green-400'
    if (duration < threshold.warning) return 'text-yellow-400'
    return 'text-red-400'
  }

  if (!isVisible) {
    return (
      <button
        onClick={onToggle}
        className="fixed bottom-4 right-4 p-3 bg-neutral-800 hover:bg-neutral-700 rounded-full shadow-lg transition-colors z-40"
        title="Show performance monitor"
      >
        <Activity className="w-5 h-5 text-neutral-400" />
      </button>
    )
  }

  return (
    <div className="fixed bottom-4 right-4 w-80 bg-neutral-900/95 backdrop-blur-lg border border-neutral-700 rounded-lg shadow-xl z-40">
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-neutral-700">
        <div className="flex items-center gap-2">
          <Activity className="w-4 h-4 text-indigo-400" />
          <span className="text-sm font-medium text-white">Performance Monitor</span>
        </div>
        <button
          onClick={onToggle}
          className="p-1 hover:bg-neutral-700 rounded transition-colors"
        >
          <span className="text-neutral-400">×</span>
        </button>
      </div>

      {/* Current Stats */}
      <div className="p-3 space-y-2">
        <div className="text-xs font-medium text-neutral-300 mb-2">Current Session</div>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Clock className="w-3 h-3 text-blue-400" />
            <span className="text-xs text-neutral-400">Image Processing</span>
          </div>
          <span className={`text-xs font-mono ${getPerformanceColor(stats.imageProcessingTime, 'image')}`}>
            {formatDuration(stats.imageProcessingTime)}
          </span>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Cpu className="w-3 h-3 text-green-400" />
            <span className="text-xs text-neutral-400">OCR Processing</span>
          </div>
          <span className={`text-xs font-mono ${getPerformanceColor(stats.ocrProcessingTime, 'ocr')}`}>
            {formatDuration(stats.ocrProcessingTime)}
          </span>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <HardDrive className="w-3 h-3 text-purple-400" />
            <span className="text-xs text-neutral-400">File Upload</span>
          </div>
          <span className={`text-xs font-mono ${getPerformanceColor(stats.fileUploadTime, 'upload')}`}>
            {formatDuration(stats.fileUploadTime)}
          </span>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Activity className="w-3 h-3 text-indigo-400" />
            <span className="text-xs text-neutral-400">AI Intelligence</span>
          </div>
          <span className={`text-xs font-mono ${getPerformanceColor(stats.fileIntelligenceTime, 'intelligence')}`}>
            {formatDuration(stats.fileIntelligenceTime)}
          </span>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Activity className="w-3 h-3 text-orange-400" />
            <span className="text-xs text-neutral-400">Memory Usage</span>
          </div>
          <span className="text-xs font-mono text-orange-400">
            {stats.memoryUsage.toFixed(1)}%
          </span>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Cpu className="w-3 h-3 text-cyan-400" />
            <span className="text-xs text-neutral-400">Active Processes</span>
          </div>
          <span className="text-xs font-mono text-cyan-400">
            {stats.activeProcesses}
          </span>
        </div>
      </div>

      {/* Processing History */}
      {processingHistory.length > 0 && (
        <div className="border-t border-neutral-700">
          <div className="p-3">
            <div className="text-xs font-medium text-neutral-300 mb-2">Recent Activity</div>
            <div className="space-y-1 max-h-32 overflow-y-auto">
              {processingHistory.slice(-5).reverse().map((entry, index) => (
                <div key={index} className="flex items-center justify-between text-xs">
                  <div className="flex items-center gap-2 min-w-0">
                    <div className={`w-2 h-2 rounded-full ${
                      entry.type === 'image' ? 'bg-blue-400' :
                      entry.type === 'ocr' ? 'bg-green-400' : 'bg-purple-400'
                    }`} />
                    <span className="text-neutral-400 truncate">
                      {entry.filename}
                    </span>
                  </div>
                  <div className="flex items-center gap-2 text-neutral-500">
                    <span>{formatTimestamp(entry.timestamp)}</span>
                    <span className={getPerformanceColor(entry.duration, entry.type)}>
                      {formatDuration(entry.duration)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Performance Tips */}
      <div className="border-t border-neutral-700 p-3">
        <div className="text-xs text-neutral-500">
          💡 Tip: Large images take longer to process. Consider resizing images over 2MB for better performance.
        </div>
      </div>
    </div>
  )
}

export default PerformanceMonitor
