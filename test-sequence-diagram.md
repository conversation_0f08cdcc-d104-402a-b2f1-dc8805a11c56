sequenceDiagram
    participant User
    participant FilesPage
    participant FileViewer
    participant MermaidDetector
    participant Ren<PERSON>er

    User->>FilesPage: Click on .md file
    FilesPage->>FileViewer: openFile(path, name)
    FileViewer->>FileViewer: loadFileContent()
    FileViewer->>MermaidDetector: isMermaidContent(content)
    
    alt Is Mermaid Diagram
        MermaidDetector-->>FileViewer: true
        FileViewer->>FileViewer: setFileType('mermaid')
        FileViewer->>Renderer: renderMermaidContent()
        Renderer-->>User: Interactive Diagram
    else Regular Markdown
        MermaidDetector-->>FileViewer: false
        FileViewer->>Renderer: renderMarkdownContent()
        Renderer-->>User: Formatted Text
    end
