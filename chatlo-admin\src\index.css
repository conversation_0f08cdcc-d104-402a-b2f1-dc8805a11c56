@tailwind base;
@tailwind components;
@tailwind utilities;

/* ChatLo Admin Dashboard Styles */
:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #0f172a;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
  background: #0f172a;
}

#root {
  width: 100%;
  min-height: 100vh;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #1e293b;
}

::-webkit-scrollbar-thumb {
  background: #475569;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Animation utilities */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-in-out;
}

.animate-pulse-slow {
  animation: pulse 2s infinite;
}

/* Custom components */
.glass-effect {
  backdrop-filter: blur(10px);
  background: rgba(15, 23, 42, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.gradient-border {
  background: linear-gradient(45deg, #3b82f6, #8b5cf6, #06b6d4);
  padding: 1px;
  border-radius: 8px;
}

.gradient-border > div {
  background: #1e293b;
  border-radius: 7px;
  padding: 1rem;
}

/* Status indicators */
.status-healthy {
  @apply bg-green-500/20 text-green-400 border-green-500/30;
}

.status-warning {
  @apply bg-yellow-500/20 text-yellow-400 border-yellow-500/30;
}

.status-error {
  @apply bg-red-500/20 text-red-400 border-red-500/30;
}

.status-info {
  @apply bg-blue-500/20 text-blue-400 border-blue-500/30;
}

/* Button variants */
.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors;
}

.btn-secondary {
  @apply bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors;
}

.btn-success {
  @apply bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors;
}

.btn-danger {
  @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors;
}

.btn-warning {
  @apply bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-4 rounded-lg transition-colors;
}

/* Card styles */
.card {
  @apply bg-gray-800 rounded-lg border border-gray-700 p-6;
}

.card-header {
  @apply border-b border-gray-700 pb-4 mb-4;
}

/* Form styles */
.form-input {
  @apply bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500;
}

.form-select {
  @apply bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500;
}

.form-textarea {
  @apply bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none;
}

/* Table styles */
.table {
  @apply w-full text-left;
}

.table th {
  @apply bg-gray-700 text-gray-300 font-medium py-3 px-4 border-b border-gray-600;
}

.table td {
  @apply py-3 px-4 border-b border-gray-700 text-gray-300;
}

.table tr:hover {
  @apply bg-gray-700/50;
}

/* Loading states */
.loading-spinner {
  @apply animate-spin rounded-full border-2 border-gray-300 border-t-blue-600;
}

.loading-dots::after {
  content: '';
  animation: dots 1.5s infinite;
}

@keyframes dots {
  0%, 20% { content: ''; }
  40% { content: '.'; }
  60% { content: '..'; }
  80%, 100% { content: '...'; }
}

/* Responsive utilities */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none;
  }
  
  .mobile-full {
    width: 100%;
  }
}

/* Dark mode optimizations */
@media (prefers-color-scheme: dark) {
  :root {
    color: #ffffff;
    background-color: #0f172a;
  }
}
