/**
 * Simple Express server for Pipeline API endpoints
 * This provides real backend endpoints for the Pipeline Configuration
 */

const express = require('express')
const cors = require('cors')
const fs = require('fs')
const path = require('path')

const app = express()
const PORT = 3001

// Middleware
app.use(cors())
app.use(express.json())

// Mock data storage
const mockData = {
  pipelines: [
    {
      id: 'pipeline-1',
      name: 'Pipeline #1',
      description: 'Master.md composition test and fine-tune',
      objective: 'Master.md composition test and fine-tune',
      process: 'Get data and report with different topics',
      successMetrics: 'Check intent match % and completion of master.md output',
      desiredResult: 'Correctly stitch JSON data and compose master.md for better user-AI communication'
    },
    {
      id: 'pipeline-2',
      name: 'Pipeline #2',
      description: 'Data analysis and insights extraction',
      objective: 'Extract meaningful insights from context data',
      process: 'Analyze all data sources and generate comprehensive reports',
      successMetrics: 'Accuracy of insights and actionable recommendations',
      desiredResult: 'Clear, actionable insights for decision making'
    }
  ],
  vaults: [
    {
      id: 'vault-1',
      name: 'Project Alpha',
      path: '/vaults/project-alpha',
      description: 'Main project documentation and data',
      fileCount: 24,
      lastModified: new Date().toISOString(),
      color: '#8AB0BB',
      icon: 'fa-folder'
    },
    {
      id: 'vault-2',
      name: 'Research Data',
      path: '/vaults/research-data',
      description: 'Research papers and analysis',
      fileCount: 12,
      lastModified: new Date(Date.now() - 86400000).toISOString(),
      color: '#FF8383',
      icon: 'fa-flask'
    }
  ],
  models: [
    'Gemma3 (32k)',
    'Gemma3 (128k)',
    'Llama3 8B',
    'Mistral 7B',
    'CodeLlama 7B'
  ]
}

// API Routes

// Get available pipelines
app.get('/api/v1/pipelines', (req, res) => {
  console.log('GET /api/v1/pipelines')
  res.json(mockData.pipelines)
})

// Get context vaults
app.get('/api/v1/vaults', (req, res) => {
  console.log('GET /api/v1/vaults')
  res.json(mockData.vaults)
})

// Get vault data sources
app.get('/api/v1/vaults/:vaultId/sources', (req, res) => {
  const { vaultId } = req.params
  console.log(`GET /api/v1/vaults/${vaultId}/sources`)
  
  const mockSources = [
    {
      id: 'source-1',
      name: 'master.md',
      type: 'markdown',
      size: 15420,
      lastModified: new Date().toISOString(),
      content: '# Master Document\n\nThis is the main documentation file...',
      path: `/vaults/${vaultId}/master.md`
    },
    {
      id: 'source-2',
      name: 'data.json',
      type: 'json',
      size: 8932,
      lastModified: new Date(Date.now() - 3600000).toISOString(),
      content: '{\n  "project": "ChatLo",\n  "version": "1.0.0"\n}',
      path: `/vaults/${vaultId}/data.json`
    },
    {
      id: 'source-3',
      name: 'config.yaml',
      type: 'yaml',
      size: 2156,
      lastModified: new Date(Date.now() - 7200000).toISOString(),
      content: 'app:\n  name: ChatLo\n  debug: true',
      path: `/vaults/${vaultId}/config.yaml`
    }
  ]
  
  res.json(mockSources)
})

// Get available models
app.get('/api/v1/models', (req, res) => {
  console.log('GET /api/v1/models')
  res.json(mockData.models)
})

// Process pipeline instruction
app.post('/api/v1/pipelines/process', async (req, res) => {
  const { pipelineId, vaultId, instruction, modelName } = req.body
  console.log(`POST /api/v1/pipelines/process - Pipeline: ${pipelineId}, Vault: ${vaultId}, Model: ${modelName}`)
  
  try {
    // Simulate processing time
    const processingTime = Math.floor(Math.random() * 3000) + 1000
    await new Promise(resolve => setTimeout(resolve, processingTime))
    
    // Generate intent matches
    const intentMatches = [
      {
        intent: 'Data Composition',
        matchPercentage: 92,
        confidence: 0.89,
        details: 'Successfully identified data composition requirements'
      },
      {
        intent: 'Master.md Generation',
        matchPercentage: 87,
        confidence: 0.85,
        details: 'Master document structure analysis completed'
      },
      {
        intent: 'JSON Data Integration',
        matchPercentage: 78,
        confidence: 0.82,
        details: 'JSON data sources successfully parsed and integrated'
      },
      {
        intent: 'User-AI Communication',
        matchPercentage: 74,
        confidence: 0.79,
        details: 'Communication optimization patterns identified'
      }
    ].sort((a, b) => b.matchPercentage - a.matchPercentage)
    
    // Generate master.md output
    const masterMdOutput = `# Generated Master Document

## Pipeline Processing Results
- **Pipeline**: ${pipelineId}
- **Vault**: ${vaultId}
- **Model**: ${modelName}
- **Instruction**: ${instruction}

## Data Analysis Summary
The pipeline successfully processed all available data sources and generated this master document based on the provided instruction.

## Key Findings
1. Data composition requirements identified
2. JSON data successfully integrated
3. Master document structure optimized
4. User-AI communication patterns enhanced

## Recommendations
- Continue monitoring intent match percentages
- Optimize data stitching algorithms
- Enhance master.md composition logic

Generated on: ${new Date().toISOString()}
Processing Time: ${processingTime}ms
`
    
    const completionScore = Math.floor(intentMatches.reduce((sum, match) => sum + match.matchPercentage, 0) / intentMatches.length)
    
    const result = {
      success: true,
      message: `Pipeline processing completed successfully with ${completionScore}% completion score`,
      timestamp: new Date().toISOString(),
      processingTime,
      completionScore,
      data: {
        analysis: 'Pipeline successfully processed all data sources and composed the master.md document',
        insights: [
          'High intent match percentage indicates good instruction clarity',
          'JSON data integration working effectively',
          'Master.md composition logic performing well'
        ],
        recommendations: [
          'Monitor intent matching trends over time',
          'Consider optimizing lower-scoring intent categories',
          'Implement feedback loop for continuous improvement'
        ]
      },
      intentMatches,
      masterMdOutput
    }
    
    res.json(result)
  } catch (error) {
    console.error('Processing error:', error)
    res.status(500).json({
      success: false,
      message: `Processing failed: ${error.message}`,
      timestamp: new Date().toISOString()
    })
  }
})

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() })
})

// Start server
app.listen(PORT, () => {
  console.log(`Pipeline API server running on http://localhost:${PORT}`)
  console.log('Available endpoints:')
  console.log('  GET  /api/v1/pipelines')
  console.log('  GET  /api/v1/vaults')
  console.log('  GET  /api/v1/vaults/:vaultId/sources')
  console.log('  GET  /api/v1/models')
  console.log('  POST /api/v1/pipelines/process')
  console.log('  GET  /health')
})
