import { BaseService, ServiceError, ServiceErrorCode, ServiceErrorContext } from './base'

import { performanceMonitor } from './performanceMonitor'
import { localModelService } from './localModelService'
import { openRouterService } from './openrouter'
import { useAppStore } from '../store'

export interface SmartInstructionRequest {
  instruction: string
  filePath: string
  vaultPath: string
  context?: {
    currentContent: string
    selectedText?: string
    cursorPosition?: { line: number; column: number }
    userIntent?: string
  }
}

export interface SmartInstructionResponse {
  success: boolean
  updatedContent: string
  changes: {
    sections: string[]
    additions: string[]
    modifications: string[]
  }
  insights: {
    detectedIntent: string
    suggestedActions: string[]
    relatedTopics: string[]
  }
  metadata: {
    processingTime: number
    confidence: number
    modelUsed?: string
    isLocalModel: boolean
  }
}

export interface MasterDocumentSection {
  id: string
  title: string
  content: string
  type: 'overview' | 'progress' | 'insights' | 'actions' | 'connections'
  priority: 'low' | 'medium' | 'high' | 'critical'
  lastUpdated: string
  tags: string[]
}

/**
 * Smart Instruction Service for processing user instructions and intelligently updating master.md
 * Uses existing LLM services (localModelService and openRouterService) for AI processing
 * Master mode focuses on local LLM usage with user notifications
 */
class SmartInstructionService extends BaseService {
  private readonly PROCESSING_VERSION = '2.0'

  constructor() {
    super({
      name: 'SmartInstructionService',
      autoInitialize: true
    })
  }

  /**
   * Initialize the service
   */
  protected async doInitialize(): Promise<void> {
    this.logger.info('Smart Instruction service initialized with LLM integration', 'doInitialize', {
      processingVersion: this.PROCESSING_VERSION
    })
  }

  /**
   * Health check implementation
   */
  protected async doHealthCheck(): Promise<boolean> {
    try {
      const canProcess = performanceMonitor.canProcess('immediate')
      this.logger.info('Health check completed', 'doHealthCheck', { canProcess })
      return canProcess
    } catch (error) {
      this.logger.warn('Health check failed', 'doHealthCheck', error)
      return false
    }
  }

  /**
   * Cleanup implementation
   */
  protected async doCleanup(): Promise<void> {
    this.logger.info('Smart Instruction service cleaned up', 'doCleanup')
  }

  /**
   * Process a smart instruction and update master.md accordingly using LLM
   */
  async processSmartInstruction(request: SmartInstructionRequest): Promise<SmartInstructionResponse> {
    return await this.executeOperationOrThrow(
      'processSmartInstruction',
      async () => {
        const startTime = performance.now()

        this.logger.info('Processing smart instruction with LLM', 'processSmartInstruction', {
          instruction: request.instruction.substring(0, 100) + '...',
          filePath: request.filePath,
          vaultPath: request.vaultPath
        })

        try {
          // 1. Get current app state and determine LLM to use
          const appState = useAppStore.getState()
          const { isPrivateMode, settings, localModelsAvailable } = appState
          
          // Determine if we should use local model (Master mode preference)
          const useLocalModel = this.shouldUseLocalModel(settings.selectedModel, isPrivateMode)
          
          // 2. Validate model availability
          if (useLocalModel && !localModelsAvailable) {
            throw new ServiceError(
              ServiceErrorCode.VALIDATION_ERROR,
              'Local LLM is required for Master mode but no local models are available. Please install Ollama or LM Studio.',
              { serviceName: this.serviceName, operation: 'processSmartInstruction' }
            )
          }

          if (useLocalModel && !settings.selectedModel) {
            throw new ServiceError(
              ServiceErrorCode.VALIDATION_ERROR,
              'Please select a local model to continue with Master mode Smart Instructions.',
              { serviceName: this.serviceName, operation: 'processSmartInstruction' }
            )
          }

          if (!useLocalModel && !settings.openRouterApiKey) {
            throw new ServiceError(
              ServiceErrorCode.VALIDATION_ERROR,
              'Please set your OpenRouter API key in settings to use external models.',
              { serviceName: this.serviceName, operation: 'processSmartInstruction' }
            )
          }

          // 3. Create LLM prompt for smart instruction processing
          const llmPrompt = this.createSmartInstructionPrompt(request)
          
          // 4. Prepare messages for LLM
          const messages = [
            {
              role: 'system' as const,
              content: this.getSystemPrompt()
            },
            {
              role: 'user' as const,
              content: llmPrompt
            }
          ]

          // 5. Call LLM using existing services
          let response: string
          let modelUsed: string
          let isLocalModel: boolean

          if (useLocalModel) {
            // Use local model service
            response = await localModelService.sendMessage(
              settings.selectedModel!,
              messages
            )
            modelUsed = settings.selectedModel!
            isLocalModel = true
            
            // Show notification for local LLM usage
            this.showLocalLLMNotification(modelUsed)
          } else {
            // Use OpenRouter service
            openRouterService.setApiKey(settings.openRouterApiKey!)
            response = await openRouterService.createChatCompletion({
              model: settings.selectedModel || 'openai/gpt-3.5-turbo',
              messages,
              temperature: 0.3,
              max_tokens: 2000
            })
            modelUsed = settings.selectedModel || 'openai/gpt-3.5-turbo'
            isLocalModel = false
          }

          // 6. Parse LLM response and extract structured data
          const parsedResponse = await this.parseLLMResponse(response)
          
          // 7. Update master.md content based on LLM response
          const updatedContent = await this.updateMasterContent(
            request.context?.currentContent || '',
            parsedResponse,
            request.instruction
          )

          const processingTime = performance.now() - startTime

          this.logger.info('Smart instruction processed successfully with LLM', 'processSmartInstruction', {
            processingTime,
            modelUsed,
            isLocalModel,
            changesCount: parsedResponse.changes.sections.length + parsedResponse.changes.additions.length + parsedResponse.changes.modifications.length
          })

          return {
            success: true,
            updatedContent,
            changes: parsedResponse.changes,
            insights: {
              detectedIntent: parsedResponse.intent,
              suggestedActions: parsedResponse.suggestedActions,
              relatedTopics: parsedResponse.relatedTopics
            },
            metadata: {
              processingTime,
              confidence: parsedResponse.confidence,
              modelUsed,
              isLocalModel
            }
          }

        } catch (error) {
          this.logger.error('Smart instruction processing failed', 'processSmartInstruction', error as Error)
          throw new ServiceError(
            ServiceErrorCode.OPERATION_FAILED,
            'Failed to process smart instruction',
            error as ServiceErrorContext
          )
        }
      }
    )
  }

  /**
   * Determine if local model should be used (reusing store logic)
   */
  private shouldUseLocalModel(selectedModel?: string, isPrivateMode?: boolean): boolean {
    // Check if selected model is local (starts with "ollama:" or "lmstudio:")
    if (selectedModel && (selectedModel.startsWith('ollama:') || selectedModel.startsWith('lmstudio:'))) {
      return true
    }

    // If private mode is on and no specific model selected, use local models
    if (isPrivateMode) {
      return true
    }

    // If private mode is off and no local model selected, use external models
    return false
  }

  /**
   * Create system prompt for Smart Instruction processing
   */
  private getSystemPrompt(): string {
    return `You are an AI assistant specialized in processing Smart Instructions for master.md documents in a local-first AI environment.

Your role is to:
1. Analyze user instructions and understand their intent
2. Intelligently update master.md content based on the instruction
3. Maintain document structure and formatting
4. Provide structured responses in JSON format

RESPONSE FORMAT:
You must respond with a valid JSON object containing:
{
  "intent": "detected user intent (research|implementation|review|planning|organization|general)",
  "confidence": 0.0-1.0,
  "updatedContent": "the complete updated master.md content",
  "changes": {
    "sections": ["list of sections added/modified"],
    "additions": ["list of new content added"],
    "modifications": ["list of existing content modified"]
  },
  "suggestedActions": ["list of suggested next actions"],
  "relatedTopics": ["list of related topics identified"]
}

IMPORTANT:
- Always respond with valid JSON
- Preserve existing content structure
- Add timestamps for new sections
- Maintain markdown formatting
- Focus on actionable insights and structured organization`
  }

  /**
   * Create prompt for Smart Instruction processing
   */
  private createSmartInstructionPrompt(request: SmartInstructionRequest): string {
    const currentContent = request.context?.currentContent || ''
    const instruction = request.instruction
    
    return `SMART INSTRUCTION: ${instruction}

CURRENT MASTER.MD CONTENT:
${currentContent}

TASK: Process the smart instruction and update the master.md content accordingly.

INSTRUCTIONS:
1. Analyze the user's instruction and determine the intent
2. Update the master.md content to reflect the instruction
3. Add or modify relevant sections as needed
4. Maintain the document's structure and formatting
5. Add timestamps for new sections
6. Provide actionable insights and suggestions

Please respond with the complete updated master.md content and structured analysis in the required JSON format.`
  }

  /**
   * Parse LLM response and extract structured data
   */
  private async parseLLMResponse(response: string): Promise<{
    intent: string
    confidence: number
    updatedContent: string
    changes: {
      sections: string[]
      additions: string[]
      modifications: string[]
    }
    suggestedActions: string[]
    relatedTopics: string[]
  }> {
    try {
      // Try to parse as JSON first
      const jsonMatch = response.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0])
        return {
          intent: parsed.intent || 'general',
          confidence: parsed.confidence || 0.7,
          updatedContent: parsed.updatedContent || response,
          changes: {
            sections: parsed.changes?.sections || [],
            additions: parsed.changes?.additions || [],
            modifications: parsed.changes?.modifications || []
          },
          suggestedActions: parsed.suggestedActions || [],
          relatedTopics: parsed.relatedTopics || []
        }
      }
    } catch (error) {
      this.logger.warn('Failed to parse LLM response as JSON, using fallback', 'parseLLMResponse', error)
    }

    // Fallback: extract content and provide basic structure
    const timestamp = new Date().toLocaleString()
    const fallbackContent = response.includes('##') ? response : `\n\n## Smart Instruction Update\n*Last updated: ${timestamp}*\n\n${response}\n`
    
    return {
      intent: 'general',
      confidence: 0.5,
      updatedContent: fallbackContent,
      changes: {
        sections: ['Smart Instruction Update'],
        additions: [response],
        modifications: []
      },
      suggestedActions: ['Review the updated content', 'Refine the instruction if needed'],
      relatedTopics: []
    }
  }

  /**
   * Update master.md content with LLM response
   */
  private async updateMasterContent(
    currentContent: string,
    parsedResponse: any,
    originalInstruction: string
  ): Promise<string> {
    // If LLM provided complete updated content, use it
    if (parsedResponse.updatedContent && parsedResponse.updatedContent !== currentContent) {
      return parsedResponse.updatedContent
    }

    // Otherwise, append the LLM response to existing content
    const timestamp = new Date().toLocaleString()
    const updateSection = `\n\n## Smart Instruction: ${timestamp}\n\n### Instruction\n${originalInstruction}\n\n### AI Response\n${parsedResponse.updatedContent || 'No specific content generated'}\n\n### Changes Made\n${parsedResponse.changes.sections.map((s: string) => `- ${s}`).join('\n')}\n\n### Suggested Actions\n${parsedResponse.suggestedActions.map((a: string) => `- [ ] ${a}`).join('\n')}\n`

    return currentContent + updateSection
  }

  /**
   * Show notification for local LLM usage
   */
  private showLocalLLMNotification(modelName: string): void {
    // Create a simple notification system
    const notification = {
      id: `smart-instruction-${Date.now()}`,
      type: 'info' as const,
      title: 'Local LLM Active',
      message: `Smart Instruction processed using local model: ${modelName}`,
      duration: 3000
    }

    // Dispatch custom event for notification system
    window.dispatchEvent(new CustomEvent('showNotification', { detail: notification }))
    
    this.logger.info('Local LLM notification shown', 'showLocalLLMNotification', { modelName })
  }
}

// Export singleton instance
export const smartInstructionService = new SmartInstructionService() 