import React, { useState, useEffect } from 'react'
import { chatLoAPI } from '../services/ChatLoAPI'

interface Deployment {
  id: string
  name: string
  version: string
  status: 'pending' | 'deploying' | 'deployed' | 'failed' | 'rolling-back'
  environment: 'development' | 'staging' | 'production'
  deployedAt: string
  deployedBy: string
  description: string
  health: 'healthy' | 'degraded' | 'unhealthy'
}

export const DeploymentManager: React.FC = () => {
  const [deployments, setDeployments] = useState<Deployment[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [selectedEnvironment, setSelectedEnvironment] = useState<string>('all')
  const [isDeploying, setIsDeploying] = useState(false)

  useEffect(() => {
    loadDeployments()
  }, [])

  const loadDeployments = async () => {
    try {
      setIsLoading(true)
      // Mock data for now - replace with actual API call
      const mockDeployments: Deployment[] = [
        {
          id: 'deploy-1',
          name: 'ChatLo Core',
          version: 'v2.1.3',
          status: 'deployed',
          environment: 'production',
          deployedAt: '2025-08-02T10:30:00Z',
          deployedBy: '<EMAIL>',
          description: 'Core application with new Smart Instruct features',
          health: 'healthy'
        },
        {
          id: 'deploy-2',
          name: 'ChatLo Admin',
          version: 'v1.0.0',
          status: 'deployed',
          environment: 'production',
          deployedAt: '2025-08-01T15:45:00Z',
          deployedBy: '<EMAIL>',
          description: 'Admin dashboard with intelligence testing',
          health: 'healthy'
        },
        {
          id: 'deploy-3',
          name: 'ChatLo Core',
          version: 'v2.2.0-beta',
          status: 'deploying',
          environment: 'staging',
          deployedAt: '2025-08-02T14:20:00Z',
          deployedBy: '<EMAIL>',
          description: 'Beta release with pipeline configuration',
          health: 'healthy'
        },
        {
          id: 'deploy-4',
          name: 'Model Service',
          version: 'v1.5.2',
          status: 'failed',
          environment: 'staging',
          deployedAt: '2025-08-02T09:15:00Z',
          deployedBy: '<EMAIL>',
          description: 'Model management service update',
          health: 'unhealthy'
        }
      ]
      setDeployments(mockDeployments)
    } catch (error) {
      console.error('Failed to load deployments:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const deployNewVersion = async () => {
    try {
      setIsDeploying(true)
      // Mock deployment process
      await new Promise(resolve => setTimeout(resolve, 3000))
      
      const newDeployment: Deployment = {
        id: `deploy-${Date.now()}`,
        name: 'ChatLo Core',
        version: 'v2.2.0',
        status: 'deployed',
        environment: 'staging',
        deployedAt: new Date().toISOString(),
        deployedBy: '<EMAIL>',
        description: 'Latest version with Pipeline 1 features',
        health: 'healthy'
      }
      
      setDeployments(prev => [newDeployment, ...prev])
    } catch (error) {
      console.error('Deployment failed:', error)
    } finally {
      setIsDeploying(false)
    }
  }

  const rollbackDeployment = async (deploymentId: string) => {
    try {
      setDeployments(prev => prev.map(d => 
        d.id === deploymentId 
          ? { ...d, status: 'rolling-back' as const }
          : d
      ))
      
      // Mock rollback process
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      setDeployments(prev => prev.map(d => 
        d.id === deploymentId 
          ? { ...d, status: 'deployed' as const, version: 'v2.1.3' }
          : d
      ))
    } catch (error) {
      console.error('Rollback failed:', error)
    }
  }

  const filteredDeployments = selectedEnvironment === 'all' 
    ? deployments 
    : deployments.filter(d => d.environment === selectedEnvironment)

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'deployed': return 'text-green-400 bg-green-900/30 border-green-700'
      case 'deploying': return 'text-blue-400 bg-blue-900/30 border-blue-700'
      case 'pending': return 'text-yellow-400 bg-yellow-900/30 border-yellow-700'
      case 'failed': return 'text-red-400 bg-red-900/30 border-red-700'
      case 'rolling-back': return 'text-orange-400 bg-orange-900/30 border-orange-700'
      default: return 'text-gray-400 bg-gray-900/30 border-gray-700'
    }
  }

  const getHealthColor = (health: string): string => {
    switch (health) {
      case 'healthy': return 'text-green-400'
      case 'degraded': return 'text-yellow-400'
      case 'unhealthy': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  const getEnvironmentColor = (environment: string): string => {
    switch (environment) {
      case 'production': return 'text-red-400 bg-red-900/30'
      case 'staging': return 'text-yellow-400 bg-yellow-900/30'
      case 'development': return 'text-blue-400 bg-blue-900/30'
      default: return 'text-gray-400 bg-gray-900/30'
    }
  }

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (isLoading) {
    return (
      <div className="h-full bg-gray-900 text-white p-6 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-500 border-t-transparent mx-auto mb-4"></div>
          <p className="text-gray-400">Loading deployments...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full bg-gray-900 text-white p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">🚀 Deployment Manager</h1>
              <p className="text-gray-400">Manage application deployments and rollbacks</p>
            </div>
            <div className="flex items-center gap-4">
              <select
                value={selectedEnvironment}
                onChange={(e) => setSelectedEnvironment(e.target.value)}
                className="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white"
              >
                <option value="all">All Environments</option>
                <option value="production">Production</option>
                <option value="staging">Staging</option>
                <option value="development">Development</option>
              </select>
              <button
                onClick={deployNewVersion}
                disabled={isDeploying}
                className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center gap-2"
              >
                {isDeploying ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent" />
                    Deploying...
                  </>
                ) : (
                  <>
                    <span>🚀</span>
                    New Deployment
                  </>
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Deployment Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-gray-400 text-sm">Total Deployments</span>
              <span className="text-lg">📦</span>
            </div>
            <div className="text-2xl font-bold text-white">{deployments.length}</div>
          </div>
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-gray-400 text-sm">Active</span>
              <span className="text-lg">✅</span>
            </div>
            <div className="text-2xl font-bold text-green-400">
              {deployments.filter(d => d.status === 'deployed').length}
            </div>
          </div>
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-gray-400 text-sm">In Progress</span>
              <span className="text-lg">⏳</span>
            </div>
            <div className="text-2xl font-bold text-blue-400">
              {deployments.filter(d => d.status === 'deploying' || d.status === 'pending').length}
            </div>
          </div>
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-gray-400 text-sm">Failed</span>
              <span className="text-lg">❌</span>
            </div>
            <div className="text-2xl font-bold text-red-400">
              {deployments.filter(d => d.status === 'failed').length}
            </div>
          </div>
        </div>

        {/* Deployments List */}
        <div className="bg-gray-800 rounded-lg border border-gray-700">
          <div className="p-6 border-b border-gray-700">
            <h2 className="text-xl font-semibold text-white">Recent Deployments</h2>
          </div>
          <div className="divide-y divide-gray-700">
            {filteredDeployments.map((deployment) => (
              <div key={deployment.id} className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-4">
                    <div>
                      <h3 className="text-lg font-medium text-white">{deployment.name}</h3>
                      <p className="text-sm text-gray-400">{deployment.description}</p>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className={`text-xs px-2 py-1 rounded border ${getEnvironmentColor(deployment.environment)}`}>
                        {deployment.environment}
                      </span>
                      <span className={`text-xs px-2 py-1 rounded border ${getStatusColor(deployment.status)}`}>
                        {deployment.status}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {deployment.status === 'deployed' && deployment.environment !== 'production' && (
                      <button
                        onClick={() => rollbackDeployment(deployment.id)}
                        className="bg-orange-600 hover:bg-orange-700 text-white text-sm px-3 py-1 rounded transition-colors"
                      >
                        Rollback
                      </button>
                    )}
                    <button className="bg-gray-600 hover:bg-gray-700 text-white text-sm px-3 py-1 rounded transition-colors">
                      View Logs
                    </button>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-gray-400">Version:</span>
                    <div className="font-medium text-white">{deployment.version}</div>
                  </div>
                  <div>
                    <span className="text-gray-400">Deployed By:</span>
                    <div className="font-medium text-white">{deployment.deployedBy}</div>
                  </div>
                  <div>
                    <span className="text-gray-400">Deployed At:</span>
                    <div className="font-medium text-white">{formatDate(deployment.deployedAt)}</div>
                  </div>
                  <div>
                    <span className="text-gray-400">Health:</span>
                    <div className={`font-medium ${getHealthColor(deployment.health)}`}>
                      {deployment.health}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
              <span>⚡</span>
              Quick Deploy
            </h3>
            <p className="text-gray-400 text-sm mb-4">Deploy the latest version to staging environment</p>
            <button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors">
              Deploy to Staging
            </button>
          </div>

          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
              <span>🔄</span>
              Rollback
            </h3>
            <p className="text-gray-400 text-sm mb-4">Rollback to the previous stable version</p>
            <button className="w-full bg-orange-600 hover:bg-orange-700 text-white py-2 px-4 rounded-lg transition-colors">
              Emergency Rollback
            </button>
          </div>

          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
              <span>📊</span>
              Health Check
            </h3>
            <p className="text-gray-400 text-sm mb-4">Run health checks on all deployments</p>
            <button className="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition-colors">
              Run Health Check
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
