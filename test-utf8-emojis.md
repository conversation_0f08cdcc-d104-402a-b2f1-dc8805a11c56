# UTF-8 Emoji Test File

## 🎯 Problem Analysis

This file tests UTF-8 character encoding with various emojis and special characters.

### Current Navigation Pain Points

1. **🔗 Limited Deep Linking**: Can't bookmark or share specific app states
2. **📄 Context Loss**: Navigation doesn't preserve vault/file/chat context  
3. **🔍 Poor Discoverability**: Users can't easily navigate to specific content
4. **🔀 Inconsistent URL Patterns**: Mixed query params and path params
5. **🌐 No Universal Link Format**: Each page handles navigation differently

### Current State Assessment

✅ **Working**: Basic page routing (/chat, /files, /history, /settings)
❌ **Broken**: Deep linking to specific content within pages
❌ **Missing**: Universal link format for cross-page navigation
❌ **Inconsistent**: Query param handling across pages
❌ **Limited**: No breadcrumb or navigation history

## 🎯 Proposed Universal Link Format

### Core Link Structure

```
chatlo://[page]/[resource]/[action]?[context]&[params]
```

### Link Format Specification

#### 1. Homepage Links
- `chatlo://home` - Main homepage
- `chatlo://home/<USER>
- `chatlo://home/<USER>

#### 2. Chat Links
- `chatlo://chat` - Chat page (current/last chat)
- `chatlo://chat/new` - New chat
- `chatlo://chat/{chatId}` - Specific chat
- `chatlo://chat/{chatId}/message/{messageId}` - Specific message

#### 3. Files Links
- `chatlo://files` - Files page (current folder)
- `chatlo://files/folder/{path}` - Specific folder
- `chatlo://files/file/{path}` - Specific file
- `chatlo://files/file/{path}/edit` - Edit file
- `chatlo://files/search?q={query}` - File search

#### 4. Context Vault Links
- `chatlo://vault/{vaultName}` - Vault overview
- `chatlo://vault/{vaultName}/files` - Vault files
- `chatlo://vault/{vaultName}/chats` - Vault chats
- `chatlo://vault/{vaultName}/master` - Master.md

#### 5. Settings Links
- `chatlo://settings` - Main settings
- `chatlo://settings/models` - Model configuration
- `chatlo://settings/api` - API settings
- `chatlo://settings/vaults` - Vault management

### Special Characters & Unicode

This file contains various Unicode characters to test encoding:

- **Arrows**: ← → ↑ ↓ ↔ ↕ ↖ ↗ ↘ ↙
- **Math**: ∑ ∏ ∫ ∂ ∆ ∇ ± × ÷ ≠ ≤ ≥ ≈ ∞
- **Currency**: $ € £ ¥ ₹ ₽ ₿
- **Symbols**: © ® ™ § ¶ † ‡ • ‰ ‱
- **Diacritics**: café naïve résumé piñata
- **CJK**: 你好 こんにちは 안녕하세요
- **Emoji**: 🚀 🎉 💡 🔥 ⭐ 🌟 💯 🎯 🔍 📱 💻 🖥️

### Implementation Priority

1. **Phase 1**: Basic URL routing with path parameters
2. **Phase 2**: Context preservation and state management  
3. **Phase 3**: Deep linking and bookmark support
4. **Phase 4**: Universal search and navigation

This should properly display all UTF-8 characters including emojis! 🎉
