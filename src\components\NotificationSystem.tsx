import React, { useEffect, useState } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { ICONS } from './Icons/index'

interface Notification {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  duration?: number
}

const NotificationSystem: React.FC = () => {
  const [notifications, setNotifications] = useState<Notification[]>([])

  useEffect(() => {
    const handleShowNotification = (event: CustomEvent<Notification>) => {
      const notification = event.detail
      setNotifications(prev => [...prev, notification])

      // Auto-remove notification after duration
      const duration = notification.duration || 5000
      setTimeout(() => {
        setNotifications(prev => prev.filter(n => n.id !== notification.id))
      }, duration)
    }

    // Listen for notification events
    window.addEventListener('showNotification', handleShowNotification as EventListener)

    return () => {
      window.removeEventListener('showNotification', handleShowNotification as EventListener)
    }
  }, [])

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id))
  }

  const getNotificationStyles = (type: Notification['type']) => {
    switch (type) {
      case 'success':
        return 'bg-green-900/90 border-green-500/50 text-green-300'
      case 'warning':
        return 'bg-yellow-900/90 border-yellow-500/50 text-yellow-300'
      case 'error':
        return 'bg-red-900/90 border-red-500/50 text-red-300'
      case 'info':
      default:
        return 'bg-blue-900/90 border-blue-500/50 text-blue-300'
    }
  }

  const getIcon = (type: Notification['type']) => {
    switch (type) {
      case 'success':
        return ICONS.check
      case 'warning':
        return ICONS.exclamationTriangle
      case 'error':
        return ICONS.times
      case 'info':
      default:
        return ICONS.info
    }
  }

  if (notifications.length === 0) return null

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      {notifications.map((notification) => (
        <div
          key={notification.id}
          className={`flex items-start gap-3 p-4 rounded-lg border shadow-lg max-w-sm transition-all duration-300 ${getNotificationStyles(notification.type)}`}
        >
          <FontAwesomeIcon 
            icon={getIcon(notification.type)} 
            className="mt-0.5 flex-shrink-0" 
          />
          <div className="flex-1 min-w-0">
            <h4 className="font-medium text-sm mb-1">{notification.title}</h4>
            <p className="text-xs opacity-90">{notification.message}</p>
          </div>
          <button
            onClick={() => removeNotification(notification.id)}
            className="flex-shrink-0 opacity-70 hover:opacity-100 transition-opacity"
          >
            <FontAwesomeIcon icon={ICONS.times} className="text-xs" />
          </button>
        </div>
      ))}
    </div>
  )
}

export default NotificationSystem 