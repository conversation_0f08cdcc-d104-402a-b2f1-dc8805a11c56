<html><head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>tailwind.config = {
  "theme": {
    "extend": {
      "colors": {
        "primary": "#8AB0BB",
        "secondary": "#FF8383",
        "tertiary": "#1B3E68",
        "supplement1": "#D5D8E0",
        "supplement2": "#89AFBA"
      },
      "fontFamily": {
        "sans": [
          "Inter",
          "sans-serif"
        ]
      }
    }
  }
};</script>
    <script> window.FontAwesomeConfig = { autoReplaceSvg: 'nest'};</script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    
    <style>
        ::-webkit-scrollbar { display: none;}
        body { font-family: 'Inter', sans-serif; }
        .file-tree-item {
            transition: all 0.2s ease;
        }
        .file-tree-item:hover {
            background-color: rgba(138, 176, 187, 0.1);
        }
        .file-tree-item.selected {
            background-color: rgba(138, 176, 187, 0.2);
            border-left: 2px solid #8AB0BB;
        }
        .markdown-content h1 { color: #D5D8E0; font-size: 1.5rem; font-weight: 600; margin-bottom: 1rem; }
        .markdown-content h2 { color: #D5D8E0; font-size: 1.25rem; font-weight: 500; margin-bottom: 0.75rem; margin-top: 1.5rem; }
        .markdown-content h3 { color: #89AFBA; font-size: 1.125rem; font-weight: 500; margin-bottom: 0.5rem; margin-top: 1rem; }
        .markdown-content p { color: #9CA3AF; line-height: 1.6; margin-bottom: 1rem; }
        .markdown-content ul { color: #9CA3AF; margin-left: 1.5rem; margin-bottom: 1rem; }
        .markdown-content li { margin-bottom: 0.25rem; }
        .markdown-content code { background-color: #374151; color: #8AB0BB; padding: 0.125rem 0.25rem; border-radius: 0.25rem; font-size: 0.875rem; }
    </style>
<link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin=""><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;500;600;700;800;900&amp;display=swap"><style>
  .highlighted-section {
    outline: 2px solid #3F20FB;
    background-color: rgba(63, 32, 251, 0.1);
  }

  .edit-button {
    position: absolute;
    z-index: 1000;
  }

  ::-webkit-scrollbar {
    display: none;
  }

  html, body {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  </style></head><body class="bg-gray-900 text-white overflow-hidden">
    
    <!-- Window Top Bar -->
    <div id="window-top-bar" class="h-6 bg-gray-950 flex items-center justify-end px-2 border-b border-gray-800">
        <div class="flex items-center gap-1">
            <button class="w-4 h-4 flex items-center justify-center hover:bg-gray-800 rounded transition-colors">
                <i class="text-gray-400 text-xs" data-fa-i2svg=""><svg class="svg-inline--fa fa-expand" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="expand" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg=""><path fill="currentColor" d="M32 32C14.3 32 0 46.3 0 64v96c0 17.7 14.3 32 32 32s32-14.3 32-32V96h64c17.7 0 32-14.3 32-32s-14.3-32-32-32H32zM64 352c0-17.7-14.3-32-32-32s-32 14.3-32 32v96c0 17.7 14.3 32 32 32h96c17.7 0 32-14.3 32-32s-14.3-32-32-32H64V352zM320 32c-17.7 0-32 14.3-32 32s14.3 32 32 32h64v64c0 17.7 14.3 32 32 32s32-14.3 32-32V64c0-17.7-14.3-32-32-32H320zM448 352c0-17.7-14.3-32-32-32s-32 14.3-32 32v64H320c-17.7 0-32 14.3-32 32s14.3 32 32 32h96c17.7 0 32-14.3 32-32V352z"></path></svg></i>
            </button>
            <button class="w-4 h-4 flex items-center justify-center hover:bg-red-600 rounded transition-colors">
                <i class="text-gray-400 text-xs hover:text-white" data-fa-i2svg=""><svg class="svg-inline--fa fa-xmark" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="xmark" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" data-fa-i2svg=""><path fill="currentColor" d="M342.6 150.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L192 210.7 86.6 105.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L146.7 256 41.4 361.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L192 301.3 297.4 406.6c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L237.3 256 342.6 150.6z"></path></svg></i>
            </button>
        </div>
    </div>
    
    <!-- Top Navigation Bar -->
    <div id="top-nav" class="h-12 bg-gray-800 border-b border-tertiary flex items-center px-4">
        <div class="flex items-center gap-2">
            <div class="w-6 h-6 bg-primary rounded flex items-center justify-center">
                <i class="text-gray-900 text-xs" data-fa-i2svg=""><svg class="svg-inline--fa fa-comment" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="comment" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M512 240c0 114.9-114.6 208-256 208c-37.1 0-72.3-6.4-104.1-17.9c-11.9 8.7-31.3 20.6-54.3 30.6C73.6 471.1 44.7 480 16 480c-6.5 0-12.3-3.9-14.8-9.9c-2.5-6-1.1-12.8 3.4-17.4l0 0 0 0 0 0 0 0 .3-.3c.3-.3 .7-.7 1.3-1.4c1.1-1.2 2.8-3.1 4.9-5.7c4.1-5 9.6-12.4 15.2-21.6c10-16.6 19.5-38.4 21.4-62.9C17.7 326.8 0 285.1 0 240C0 125.1 114.6 32 256 32s256 93.1 256 208z"></path></svg></i>
            </div>
            <span class="text-sm font-semibold text-primary">Chatlo</span>
        </div>
        
        <div class="flex-1 flex items-center justify-center ml-12 mr-[calc(256px-48px)]">
            <div class="flex items-center gap-2 bg-tertiary/40 rounded-lg px-3 py-1 w-full max-w-2xl">
                <button class="p-1 hover:bg-tertiary/60 rounded transition-colors">
                    <i class="text-supplement2 text-xs" data-fa-i2svg=""><svg class="svg-inline--fa fa-bell" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="bell" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg=""><path fill="currentColor" d="M224 0c-17.7 0-32 14.3-32 32V51.2C119 66 64 130.6 64 208v18.8c0 47-17.3 92.4-48.5 127.6l-7.4 8.3c-8.4 9.4-10.4 22.9-5.3 34.4S19.4 416 32 416H416c12.6 0 24-7.4 29.2-18.9s3.1-25-5.3-34.4l-7.4-8.3C401.3 319.2 384 273.9 384 226.8V208c0-77.4-55-142-128-156.8V32c0-17.7-14.3-32-32-32zm45.3 493.3c12-12 18.7-28.3 18.7-45.3H224 160c0 17 6.7 33.3 18.7 45.3s28.3 18.7 45.3 18.7s33.3-6.7 45.3-18.7z"></path></svg></i>
                </button>
                <button class="p-1 hover:bg-tertiary/60 rounded transition-colors bg-primary/20 border border-primary/30">
                    <i class="text-primary text-xs" data-fa-i2svg=""><svg class="svg-inline--fa fa-file" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="file" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" data-fa-i2svg=""><path fill="currentColor" d="M0 64C0 28.7 28.7 0 64 0H224V128c0 17.7 14.3 32 32 32H384V448c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64V64zm384 64H256V0L384 128z"></path></svg></i>
                </button>
                <button class="p-1 hover:bg-tertiary/60 rounded transition-colors">
                    <i class="text-gray-400 text-xs" data-fa-i2svg=""><svg class="svg-inline--fa fa-chevron-left" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="chevron-left" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" data-fa-i2svg=""><path fill="currentColor" d="M9.4 233.4c-12.5 12.5-12.5 32.8 0 45.3l192 192c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L77.3 256 246.6 86.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0l-192 192z"></path></svg></i>
                </button>
                <div class="flex items-center gap-2 flex-1">
                    <i class="text-supplement2 text-xs" data-fa-i2svg=""><svg class="svg-inline--fa fa-folder" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="folder" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M64 480H448c35.3 0 64-28.7 64-64V160c0-35.3-28.7-64-64-64H288c-10.1 0-19.6-4.7-25.6-12.8L243.2 57.6C231.1 41.5 212.1 32 192 32H64C28.7 32 0 60.7 0 96V416c0 35.3 28.7 64 64 64z"></path></svg></i>
                    <span class="text-xs text-supplement1">Project Alpha - Design System</span>
                </div>
                <button class="p-1 hover:bg-tertiary/60 rounded transition-colors">
                    <i class="text-gray-400 text-xs" data-fa-i2svg=""><svg class="svg-inline--fa fa-chevron-right" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="chevron-right" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" data-fa-i2svg=""><path fill="currentColor" d="M310.6 233.4c12.5 12.5 12.5 32.8 0 45.3l-192 192c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3L242.7 256 73.4 86.6c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0l192 192z"></path></svg></i>
                </button>
            </div>
        </div>
        
        <div class="flex items-center gap-3">
            <div class="flex items-center gap-2">
                <span class="text-xs text-supplement1">Private</span>
                <button class="relative inline-flex h-4 w-7 items-center rounded-full bg-secondary transition-colors">
                    <span class="inline-block h-3 w-3 transform rounded-full bg-white transition-transform translate-x-3.5"></span>
                </button>
            </div>
            <button class="p-2 hover:bg-gray-700 rounded-lg transition-colors group relative">
                <i class="text-supplement1 text-sm" data-fa-i2svg=""><svg class="svg-inline--fa fa-user" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="user" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg=""><path fill="currentColor" d="M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-45.7 48C79.8 304 0 383.8 0 482.3C0 498.7 13.3 512 29.7 512H418.3c16.4 0 29.7-13.3 29.7-29.7C448 383.8 368.2 304 269.7 304H178.3z"></path></svg></i>
                <div class="absolute bottom-10 left-1/2 transform -translate-x-1/2 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                    User Profile
                </div>
            </button>
            <button class="p-2 hover:bg-gray-700 rounded-lg transition-colors group relative">
                <i class="text-supplement1 text-sm" data-fa-i2svg=""><svg class="svg-inline--fa fa-gear" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="gear" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M495.9 166.6c3.2 8.7 .5 18.4-6.4 24.6l-43.3 39.4c1.1 8.3 1.7 16.8 1.7 25.4s-.6 17.1-1.7 25.4l43.3 39.4c6.9 6.2 9.6 15.9 6.4 24.6c-4.4 11.9-9.7 23.3-15.8 34.3l-4.7 8.1c-6.6 11-14 21.4-22.1 31.2c-5.9 7.2-15.7 9.6-24.5 6.8l-55.7-17.7c-13.4 10.3-28.2 18.9-44 25.4l-12.5 57.1c-2 9.1-9 16.3-18.2 17.8c-13.8 2.3-28 3.5-42.5 3.5s-28.7-1.2-42.5-3.5c-9.2-1.5-16.2-8.7-18.2-17.8l-12.5-57.1c-15.8-6.5-30.6-15.1-44-25.4L83.1 425.9c-8.8 2.8-18.6 .3-24.5-6.8c-8.1-9.8-15.5-20.2-22.1-31.2l-4.7-8.1c-6.1-11-11.4-22.4-15.8-34.3c-3.2-8.7-.5-18.4 6.4-24.6l43.3-39.4C64.6 273.1 64 264.6 64 256s.6-17.1 1.7-25.4L22.4 191.2c-6.9-6.2-9.6-15.9-6.4-24.6c4.4-11.9 9.7-23.3 15.8-34.3l4.7-8.1c6.6-11 14-21.4 22.1-31.2c5.9-7.2 15.7-9.6 24.5-6.8l55.7 17.7c13.4-10.3 28.2-18.9 44-25.4l12.5-57.1c2-9.1 9-16.3 18.2-17.8C227.3 1.2 241.5 0 256 0s28.7 1.2 42.5 3.5c9.2 1.5 16.2 8.7 18.2 17.8l12.5 57.1c15.8 6.5 30.6 15.1 44 25.4l55.7-17.7c8.8-2.8 18.6-.3 24.5 6.8c8.1 9.8 15.5 20.2 22.1 31.2l4.7 8.1c6.1 11 11.4 22.4 15.8 34.3zM256 336a80 80 0 1 0 0-160 80 80 0 1 0 0 160z"></path></svg></i>
                <div class="absolute bottom-10 left-1/2 transform -translate-x-1/2 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                    Settings
                </div>
            </button>
        </div>
    </div>
    
    <div id="app-container" class="flex h-[calc(100vh-72px)]">
        
        <!-- VSCode-style Icon Bar -->
        <div id="icon-bar" class="w-12 bg-gray-900 border-r border-tertiary flex flex-col items-center py-2">
            <div class="flex flex-col gap-1 mb-auto">
                <button class="w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1 group relative">
                    <i data-fa-i2svg=""><svg class="svg-inline--fa fa-house" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="house" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512" data-fa-i2svg=""><path fill="currentColor" d="M575.8 255.5c0 18-15 32.1-32 32.1h-32l.7 160.2c0 2.7-.2 5.4-.5 8.1V472c0 22.1-17.9 40-40 40H456c-1.1 0-2.2 0-3.3-.1c-1.4 .1-2.8 .1-4.2 .1H416 392c-22.1 0-40-17.9-40-40V448 384c0-17.7-14.3-32-32-32H256c-17.7 0-32 14.3-32 32v64 24c0 22.1-17.9 40-40 40H160 128.1c-1.5 0-3-.1-4.5-.2c-1.2 .1-2.4 .2-3.6 .2H104c-22.1 0-40-17.9-40-40V360c0-.9 0-1.9 .1-2.8V287.6H32c-18 0-32-14-32-32.1c0-9 3-17 10-24L266.4 8c7-7 15-8 22-8s15 2 21 7L564.8 231.5c8 7 12 15 11 24z"></path></svg></i>
                    <div class="absolute left-12 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                        Home
                    </div>
                </button>
                <button class="w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1 group relative">
                    <i data-fa-i2svg=""><svg class="svg-inline--fa fa-comment" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="comment" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M512 240c0 114.9-114.6 208-256 208c-37.1 0-72.3-6.4-104.1-17.9c-11.9 8.7-31.3 20.6-54.3 30.6C73.6 471.1 44.7 480 16 480c-6.5 0-12.3-3.9-14.8-9.9c-2.5-6-1.1-12.8 3.4-17.4l0 0 0 0 0 0 0 0 .3-.3c.3-.3 .7-.7 1.3-1.4c1.1-1.2 2.8-3.1 4.9-5.7c4.1-5 9.6-12.4 15.2-21.6c10-16.6 19.5-38.4 21.4-62.9C17.7 326.8 0 285.1 0 240C0 125.1 114.6 32 256 32s256 93.1 256 208z"></path></svg></i>
                    <div class="absolute left-12 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                        Chat
                    </div>
                </button>
                <button class="w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1 group relative">
                    <i data-fa-i2svg=""><svg class="svg-inline--fa fa-clock-rotate-left" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="clock-rotate-left" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M75 75L41 41C25.9 25.9 0 36.6 0 57.9V168c0 13.3 10.7 24 24 24H134.1c21.4 0 32.1-25.9 17-41l-30.8-30.8C155 85.5 203 64 256 64c106 0 192 86 192 192s-86 192-192 192c-40.8 0-78.6-12.7-109.7-34.4c-14.5-10.1-34.4-6.6-44.6 7.9s-6.6 34.4 7.9 44.6C151.2 495 201.7 512 256 512c141.4 0 256-114.6 256-256S397.4 0 256 0C185.3 0 121.3 28.7 75 75zm181 53c-13.3 0-24 10.7-24 24V256c0 6.4 2.5 12.5 7 17l72 72c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9l-65-65V152c0-13.3-10.7-24-24-24z"></path></svg></i>
                    <div class="absolute left-12 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                        History
                    </div>
                </button>
                <button class="w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1 group relative bg-primary/20 border-l-2 border-primary">
                    <i class="text-primary" data-fa-i2svg=""><svg class="svg-inline--fa fa-folder-tree" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="folder-tree" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512" data-fa-i2svg=""><path fill="currentColor" d="M64 32C64 14.3 49.7 0 32 0S0 14.3 0 32v96V384c0 35.3 28.7 64 64 64H256V384H64V160H256V96H64V32zM288 192c0 17.7 14.3 32 32 32H544c17.7 0 32-14.3 32-32V64c0-17.7-14.3-32-32-32H445.3c-8.5 0-16.6-3.4-22.6-9.4L409.4 9.4c-6-6-14.1-9.4-22.6-9.4H320c-17.7 0-32 14.3-32 32V192zm0 288c0 17.7 14.3 32 32 32H544c17.7 0 32-14.3 32-32V352c0-17.7-14.3-32-32-32H445.3c-8.5 0-16.6-3.4-22.6-9.4l-13.3-13.3c-6-6-14.1-9.4-22.6-9.4H320c-17.7 0-32 14.3-32 32V480z"></path></svg></i>
                    <div class="absolute left-12 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                        Files
                    </div>
                </button>
            </div>
        </div>
        
        <!-- Main Files Content -->
        <div id="files-content" class="flex-1 flex bg-gray-900">
            
            <!-- Left Column - File Tree (20%) -->
            <div id="file-tree-panel" class="w-1/5 bg-gray-800 border-r border-tertiary/50 flex flex-col">
                
                <!-- File Tree Header -->
                <div class="p-4 border-b border-tertiary/50">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-2 flex-1">
                            <button class="p-1 hover:bg-gray-700 rounded transition-colors">
                                <i class="text-gray-400 text-xs" data-fa-i2svg=""><svg class="svg-inline--fa fa-chevron-left" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="chevron-left" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" data-fa-i2svg=""><path fill="currentColor" d="M9.4 233.4c-12.5 12.5-12.5 32.8 0 45.3l192 192c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L77.3 256 246.6 86.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0l-192 192z"></path></svg></i>
                            </button>
                            <div class="flex items-center gap-2 flex-1 bg-gray-700/30 rounded px-2 py-1">
                                <h3 class="font-medium text-supplement1 text-sm">Your First Context Vault</h3>
                                <i class="text-gray-400 text-xs ml-auto" data-fa-i2svg=""><svg class="svg-inline--fa fa-chevron-down" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="chevron-down" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M233.4 406.6c12.5 12.5 32.8 12.5 45.3 0l192-192c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L256 338.7 86.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l192 192z"></path></svg></i>
                            </div>
                            <button class="p-1 hover:bg-gray-700 rounded transition-colors">
                                <i class="text-gray-400 text-xs" data-fa-i2svg=""><svg class="svg-inline--fa fa-chevron-right" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="chevron-right" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" data-fa-i2svg=""><path fill="currentColor" d="M310.6 233.4c12.5 12.5 12.5 32.8 0 45.3l-192 192c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3L242.7 256 73.4 86.6c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0l192 192z"></path></svg></i>
                            </button>
                        </div>
                        <button class="p-1 hover:bg-gray-700 rounded transition-colors group relative ml-2">
                            <i class="text-gray-400 text-xs" data-fa-i2svg=""><svg class="svg-inline--fa fa-plus" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="plus" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg=""><path fill="currentColor" d="M256 80c0-17.7-14.3-32-32-32s-32 14.3-32 32V224H48c-17.7 0-32 14.3-32 32s14.3 32 32 32H192V432c0 17.7 14.3 32 32 32s32-14.3 32-32V288H400c17.7 0 32-14.3 32-32s-14.3-32-32-32H256V80z"></path></svg></i>
                            <div class="absolute bottom-6 right-0 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                                Add File
                            </div>
                        </button>
                    </div>
                </div>

                <!-- View Toggle Buttons -->
                <div class="p-3 border-b border-tertiary/50">
                    <div class="flex gap-2">
                        <button class="flex-1 flex items-center justify-center gap-2 p-2 rounded-lg bg-gray-700/50 hover:bg-gray-700 transition-colors text-supplement1">
                            <i class="text-sm" data-fa-i2svg=""><svg class="svg-inline--fa fa-sitemap" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="sitemap" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512" data-fa-i2svg=""><path fill="currentColor" d="M208 80c0-26.5 21.5-48 48-48h64c26.5 0 48 21.5 48 48v64c0 26.5-21.5 48-48 48h-8v40H464c30.9 0 56 25.1 56 56v32h8c26.5 0 48 21.5 48 48v64c0 26.5-21.5 48-48 48H464c-26.5 0-48-21.5-48-48V368c0-26.5 21.5-48 48-48h8V288c0-4.4-3.6-8-8-8H312v40h8c26.5 0 48 21.5 48 48v64c0 26.5-21.5 48-48 48H256c-26.5 0-48-21.5-48-48V368c0-26.5 21.5-48 48-48h8V280H112c-4.4 0-8 3.6-8 8v32h8c26.5 0 48 21.5 48 48v64c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V368c0-26.5 21.5-48 48-48h8V288c0-30.9 25.1-56 56-56H264V192h-8c-26.5 0-48-21.5-48-48V80z"></path></svg></i>
                            <span class="text-xs font-medium">Explorer</span>
                        </button>
                        <button class="flex-1 flex items-center justify-center gap-2 p-2 rounded-lg bg-primary/20 border border-primary/50 text-primary transition-colors">
                            <i class="text-sm" data-fa-i2svg=""><svg class="svg-inline--fa fa-lightbulb" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="lightbulb" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" data-fa-i2svg=""><path fill="currentColor" d="M272 384c9.6-31.9 29.5-59.1 49.2-86.2l0 0c5.2-7.1 10.4-14.2 15.4-21.4c19.8-28.5 31.4-63 31.4-100.3C368 78.8 289.2 0 192 0S16 78.8 16 176c0 37.3 11.6 71.9 31.4 100.3c5 7.2 10.2 14.3 15.4 21.4l0 0c19.8 27.1 39.7 54.4 49.2 86.2H272zM192 512c44.2 0 80-35.8 80-80V416H112v16c0 44.2 35.8 80 80 80zM112 176c0 8.8-7.2 16-16 16s-16-7.2-16-16c0-61.9 50.1-112 112-112c8.8 0 16 7.2 16 16s-7.2 16-16 16c-44.2 0-80 35.8-80 80z"></path></svg></i>
                            <span class="text-xs font-medium">Master</span>
                        </button>
                    </div>
                </div>
                
                <!-- File Tree -->
                <div id="file-tree" class="flex-1 overflow-y-auto p-2">
                    
                    <!-- Root Folder -->
                    <div class="file-tree-item p-2 rounded cursor-pointer flex items-center gap-2" onclick="toggleFolder('root')">
                        <i class="text-gray-400 text-xs w-3" data-fa-i2svg=""><svg class="svg-inline--fa fa-chevron-down" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="chevron-down" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M233.4 406.6c12.5 12.5 32.8 12.5 45.3 0l192-192c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L256 338.7 86.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l192 192z"></path></svg></i>
                        <i class="text-supplement2 text-sm" data-fa-i2svg=""><svg class="svg-inline--fa fa-folder" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="folder" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M64 480H448c35.3 0 64-28.7 64-64V160c0-35.3-28.7-64-64-64H288c-10.1 0-19.6-4.7-25.6-12.8L243.2 57.6C231.1 41.5 212.1 32 192 32H64C28.7 32 0 60.7 0 96V416c0 35.3 28.7 64 64 64z"></path></svg></i>
                        <span class="text-sm text-supplement1">project-alpha</span>
                        <div class="ml-auto">
                            <span class="w-5 h-5 bg-secondary/20 text-secondary text-xs rounded-full flex items-center justify-center font-medium">3</span>
                        </div>
                    </div>
                    
                    <!-- Master.md File -->
                    <div class="file-tree-item selected p-2 rounded cursor-pointer flex items-center gap-2 ml-4 bg-primary/20 border border-primary/30" onclick="selectFile('master.md')">
                        <div class="w-3"></div>
                        <i class="text-primary text-sm" data-fa-i2svg=""><svg class="svg-inline--fa fa-file-lines" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="file-lines" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" data-fa-i2svg=""><path fill="currentColor" d="M64 0C28.7 0 0 28.7 0 64V448c0 35.3 28.7 64 64 64H320c35.3 0 64-28.7 64-64V160H256c-17.7 0-32-14.3-32-32V0H64zM256 0V128H384L256 0zM112 256H272c8.8 0 16 7.2 16 16s-7.2 16-16 16H112c-8.8 0-16-7.2-16-16s7.2-16 16-16zm0 64H272c8.8 0 16 7.2 16 16s-7.2 16-16 16H112c-8.8 0-16-7.2-16-16s7.2-16 16-16zm0 64H272c8.8 0 16 7.2 16 16s-7.2 16-16 16H112c-8.8 0-16-7.2-16-16s7.2-16 16-16z"></path></svg></i>
                        <span class="text-sm text-primary font-medium">master.md</span>
                        <div class="ml-auto">
                            <div class="w-2 h-2 bg-primary rounded-full"></div>
                        </div>
                    </div>
                    
                    <!-- Design Folder -->
                    <div class="file-tree-item p-2 rounded cursor-pointer flex items-center gap-2 ml-4" onclick="toggleFolder('design')">
                        <i class="text-gray-400 text-xs w-3" data-fa-i2svg=""><svg class="svg-inline--fa fa-chevron-right" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="chevron-right" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" data-fa-i2svg=""><path fill="currentColor" d="M310.6 233.4c12.5 12.5 12.5 32.8 0 45.3l-192 192c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3L242.7 256 73.4 86.6c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0l192 192z"></path></svg></i>
                        <i class="text-supplement2 text-sm" data-fa-i2svg=""><svg class="svg-inline--fa fa-folder" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="folder" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M64 480H448c35.3 0 64-28.7 64-64V160c0-35.3-28.7-64-64-64H288c-10.1 0-19.6-4.7-25.6-12.8L243.2 57.6C231.1 41.5 212.1 32 192 32H64C28.7 32 0 60.7 0 96V416c0 35.3 28.7 64 64 64z"></path></svg></i>
                        <span class="text-sm text-supplement1">design</span>
                        <div class="ml-auto">
                            <span class="w-4 h-4 bg-supplement2/20 text-supplement2 text-xs rounded-full flex items-center justify-center font-medium">2</span>
                        </div>
                    </div>
                    
                    <!-- Components Folder -->
                    <div class="file-tree-item p-2 rounded cursor-pointer flex items-center gap-2 ml-4" onclick="toggleFolder('components')">
                        <i class="text-gray-400 text-xs w-3" data-fa-i2svg=""><svg class="svg-inline--fa fa-chevron-down" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="chevron-down" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M233.4 406.6c12.5 12.5 32.8 12.5 45.3 0l192-192c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L256 338.7 86.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l192 192z"></path></svg></i>
                        <i class="text-supplement2 text-sm" data-fa-i2svg=""><svg class="svg-inline--fa fa-folder" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="folder" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M64 480H448c35.3 0 64-28.7 64-64V160c0-35.3-28.7-64-64-64H288c-10.1 0-19.6-4.7-25.6-12.8L243.2 57.6C231.1 41.5 212.1 32 192 32H64C28.7 32 0 60.7 0 96V416c0 35.3 28.7 64 64 64z"></path></svg></i>
                        <span class="text-sm text-supplement1">components</span>
                        <div class="ml-auto">
                            <span class="w-4 h-4 bg-supplement2/20 text-supplement2 text-xs rounded-full flex items-center justify-center font-medium">2</span>
                        </div>
                    </div>
                    
                    <!-- Component Files -->
                    <div class="file-tree-item p-2 rounded cursor-pointer flex items-center gap-2 ml-8" onclick="selectFile('buttons.md')">
                        <div class="w-3"></div>
                        <i class="text-secondary text-sm" data-fa-i2svg=""><svg class="svg-inline--fa fa-file-lines" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="file-lines" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" data-fa-i2svg=""><path fill="currentColor" d="M64 0C28.7 0 0 28.7 0 64V448c0 35.3 28.7 64 64 64H320c35.3 0 64-28.7 64-64V160H256c-17.7 0-32-14.3-32-32V0H64zM256 0V128H384L256 0zM112 256H272c8.8 0 16 7.2 16 16s-7.2 16-16 16H112c-8.8 0-16-7.2-16-16s7.2-16 16-16zm0 64H272c8.8 0 16 7.2 16 16s-7.2 16-16 16H112c-8.8 0-16-7.2-16-16s7.2-16 16-16zm0 64H272c8.8 0 16 7.2 16 16s-7.2 16-16 16H112c-8.8 0-16-7.2-16-16s7.2-16 16-16z"></path></svg></i>
                        <span class="text-sm text-secondary">buttons.md</span>
                        <div class="ml-auto">
                            <div class="w-2 h-2 bg-secondary rounded-full"></div>
                        </div>
                    </div>
                    
                    <div class="file-tree-item p-2 rounded cursor-pointer flex items-center gap-2 ml-8" onclick="selectFile('forms.md')">
                        <div class="w-3"></div>
                        <i class="text-gray-400 text-sm" data-fa-i2svg=""><svg class="svg-inline--fa fa-file-lines" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="file-lines" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" data-fa-i2svg=""><path fill="currentColor" d="M64 0C28.7 0 0 28.7 0 64V448c0 35.3 28.7 64 64 64H320c35.3 0 64-28.7 64-64V160H256c-17.7 0-32-14.3-32-32V0H64zM256 0V128H384L256 0zM112 256H272c8.8 0 16 7.2 16 16s-7.2 16-16 16H112c-8.8 0-16-7.2-16-16s7.2-16 16-16zm0 64H272c8.8 0 16 7.2 16 16s-7.2 16-16 16H112c-8.8 0-16-7.2-16-16s7.2-16 16-16zm0 64H272c8.8 0 16 7.2 16 16s-7.2 16-16 16H112c-8.8 0-16-7.2-16-16s7.2-16 16-16z"></path></svg></i>
                        </div></div></div>
            
            <!-- Right Column (80%) -->
            <div id="main-panel" class="flex-1 flex flex-col">
                
                <!-- Top Row - Master.md Preview (60%) -->
                <div id="markdown-preview-section" class="h-[60%] flex border-b border-tertiary/50">
                    
                    <!-- Markdown Content -->
                    <div id="markdown-content" class="flex-1 overflow-y-auto p-6">
                        <div class="markdown-content">
                            <h1 class="">Project Alpha Design System</h1>
                            <p class="">A comprehensive design system for modern web applications, built with accessibility and scalability in mind.</p>
                            
                            <h2 class="">Overview</h2>
                            <p class="">This design system provides a unified set of components, tokens, and guidelines to ensure consistency across all product interfaces. It includes everything from basic UI elements to complex interaction patterns.</p>
                            
                            <h3 class="">Key Features</h3>
                            <ul>
                                <li class="">Comprehensive component library with 50+ components</li>
                                <li class="">Design tokens for colors, typography, spacing, and shadows</li>
                                <li class="">Accessibility guidelines and WCAG 2.1 AA compliance</li>
                                <li class="">Dark and light theme support</li>
                                <li class="">Responsive design patterns</li>
                                <li class="">Interactive documentation and examples</li>
                            </ul>
                            
                            <h2 class="">Getting Started</h2>
                            <p class="">To begin using the design system, install the package and import the necessary components:</p>
                            
                            <p><code>npm install @company/design-system</code></p>
                            
                            <h3 class="">Basic Usage</h3>
                            <p class="">Import components as needed in your application. Each component comes with full TypeScript support and comprehensive documentation.</p>
                            
                            <h2 class="">Component Categories</h2>
                            
                            <h3 class="">Foundation</h3>
                            <ul>
                                <li class="">Colors and themes</li>
                                <li class="">Typography scales</li>
                                <li class="">Spacing and layout</li>
                                <li class="">Icons and illustrations</li>
                            </ul>
                            
                            <h3 class="">Components</h3>
                            <ul>
                                <li class="">Buttons and actions</li>
                                <li class="">Forms and inputs</li>
                                <li class="">Navigation elements</li>
                                <li class="">Data display</li>
                                <li class="">Feedback and overlays</li>
                            </ul>
                            
                            <h2 class="">Contribution Guidelines</h2>
                            <p class="">We welcome contributions to the design system. Please follow our established patterns and ensure all new components meet our quality standards.</p>
                        </div>
                    </div>
                    
                    <!-- Prompt Action Box -->
                    <div id="prompt-action-box" class="w-80 bg-gray-800 border-l border-tertiary/50 p-4 flex flex-col">
    <div class="mb-4">
        <h4 class="font-medium text-supplement1 mb-2 text-sm">Context Management</h4>
    </div>
    
    <div class="space-y-2">
        <button class="w-full p-2 bg-primary/20 border border-primary/50 rounded-lg text-left hover:bg-primary/30 transition-colors">
            <div class="flex items-center gap-2">
                <i class="text-primary text-sm" data-fa-i2svg=""><svg class="svg-inline--fa fa-circle-question" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="circle-question" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM169.8 165.3c7.9-22.3 29.1-37.3 52.8-37.3h58.3c34.9 0 63.1 28.3 63.1 63.1c0 22.6-12.1 43.5-31.7 54.8L280 264.4c-.2 13-10.9 23.6-24 23.6c-13.3 0-24-10.7-24-24V250.5c0-8.6 4.6-16.5 12.1-20.8l44.3-25.4c4.7-2.7 7.6-7.7 7.6-13.1c0-8.4-6.8-15.1-15.1-15.1H222.6c-3.4 0-6.4 2.1-7.5 5.3l-.4 1.2c-4.4 12.5-18.2 19-30.6 14.6s-19-18.2-14.6-30.6l.4-1.2zM224 352a32 32 0 1 1 64 0 32 32 0 1 1 -64 0z"></path></svg></i>
                <div>
                    <p class="text-xs font-medium text-supplement1">Ask about this file</p>
                    <p class="text-xs text-gray-400">Get insights</p>
                </div>
            </div>
        </button>
        
        <button class="w-full p-2 bg-secondary/20 border border-secondary/50 rounded-lg text-left hover:bg-secondary/30 transition-colors">
            <div class="flex items-center gap-2">
                <i class="text-secondary text-sm" data-fa-i2svg=""><svg class="svg-inline--fa fa-compress" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="compress" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg=""><path fill="currentColor" d="M160 64c0-17.7-14.3-32-32-32s-32 14.3-32 32v64H32c-17.7 0-32 14.3-32 32s14.3 32 32 32h96c17.7 0 32-14.3 32-32V64zM32 320c-17.7 0-32 14.3-32 32s14.3 32 32 32H96v64c0 17.7 14.3 32 32 32s32-14.3 32-32V352c0-17.7-14.3-32-32-32H32zM352 64c0-17.7-14.3-32-32-32s-32 14.3-32 32v96c0 17.7 14.3 32 32 32h96c17.7 0 32-14.3 32-32s-14.3-32-32-32H352V64zM320 320c-17.7 0-32 14.3-32 32v96c0 17.7 14.3 32 32 32s32-14.3 32-32V384h64c17.7 0 32-14.3 32-32s-14.3-32-32-32H320z"></path></svg></i>
                <div>
                    <p class="text-xs font-medium text-supplement1">Summarize</p>
                    <p class="text-xs text-gray-400">Brief summary</p>
                </div>
            </div>
        </button>
        
        <button class="w-full p-2 bg-supplement2/20 border border-supplement2/50 rounded-lg text-left hover:bg-supplement2/30 transition-colors">
            <div class="flex items-center gap-2">
                <i class="text-supplement2 text-sm" data-fa-i2svg=""><svg class="svg-inline--fa fa-pen-to-square" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="pen-to-square" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M471.6 21.7c-21.9-21.9-57.3-21.9-79.2 0L362.3 51.7l97.9 97.9 30.1-30.1c21.9-21.9 21.9-57.3 0-79.2L471.6 21.7zm-299.2 220c-6.1 6.1-10.8 13.6-13.5 21.9l-29.6 88.8c-2.9 8.6-.6 18.1 5.8 24.6s15.9 8.7 24.6 5.8l88.8-29.6c8.2-2.7 15.7-7.4 21.9-13.5L437.7 172.3 339.7 74.3 172.4 241.7zM96 64C43 64 0 107 0 160V416c0 53 43 96 96 96H352c53 0 96-43 96-96V320c0-17.7-14.3-32-32-32s-32 14.3-32 32v96c0 17.7-14.3 32-32 32H96c-17.7 0-32-14.3-32-32V160c0-17.7 14.3-32 32-32h96c17.7 0 32-14.3 32-32s-14.3-32-32-32H96z"></path></svg></i>
                <div>
                    <p class="text-xs font-medium text-supplement1">Edit content</p>
                    <p class="text-xs text-gray-400">Make improvements</p>
                </div>
            </div>
        </button>
    </div>
    
    <div class="flex-1"></div>
    
    <div class="border-t border-tertiary/50 pt-4">
        <textarea placeholder="Ask anything about this file..." class="w-full h-20 bg-gray-700 border border-tertiary/50 rounded-lg p-3 text-xs text-supplement1 placeholder-gray-400 resize-none focus:outline-none focus:border-primary/50"></textarea>
        <button class="w-full mt-3 p-2 bg-primary text-gray-900 rounded-lg font-medium hover:bg-primary/80 transition-colors text-sm">
            Smart Instruction</button>
    </div>
</div>
                </div>
                
                <!-- Bottom Row - Recent Chats (40%) -->
                <div id="recent-chats-section" class="h-[40%] bg-gray-850/70">
    
    <!-- Folder Divider -->
    <div class="flex items-center justify-center py-1 cursor-pointer hover:bg-gray-700/50 transition-colors group">
        <div class="w-12 h-1 bg-gray-600 rounded-full group-hover:bg-primary/60 transition-colors"></div>
    </div>
    
    <!-- Header -->
    <div class="p-4 border-b border-tertiary/50">
        <div class="flex items-center justify-between">
            <h3 class="font-medium text-supplement1">Recent Activity</h3>
            <button class="text-xs text-primary hover:text-primary/80 transition-colors">View All</button>
        </div>
    </div>
    
    <!-- Two Column Layout -->
    <div class="flex h-full">
        
        <!-- Recent Chats Column -->
        <div class="w-1/2 border-r border-tertiary/50">
            <div class="p-3 border-b border-tertiary/30">
                <h4 class="text-xs font-medium text-supplement1">Recent Chats</h4>
            </div>
            <div class="overflow-y-auto p-3 h-full">
                <div class="space-y-2">
                    
                    <!-- Chat Item 1 -->
                    <div class="p-3 bg-gray-700/50 rounded-lg hover:bg-gray-700 cursor-pointer transition-colors">
                        <div class="flex items-start justify-between mb-2">
                            <div class="flex-1">
                                <h5 class="font-medium text-xs text-supplement1 mb-1">Component Architecture Discussion</h5>
                                <p class="text-xs text-gray-400 line-clamp-2">How should we structure the component hierarchy for better maintainability? I'm thinking about separating atomic components...</p>
                            </div>
                            <div class="flex items-center gap-1 ml-2">
                                <span class="text-xs text-gray-500">2h ago</span>
                                <button class="p-1 hover:bg-gray-600 rounded transition-colors">
                                    <i class="text-gray-400 text-xs" data-fa-i2svg=""><svg class="svg-inline--fa fa-reply" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="reply" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M205 34.8c11.5 5.1 19 16.6 19 29.2v64H336c97.2 0 176 78.8 176 176c0 113.3-81.5 163.9-100.2 174.1c-2.5 1.4-5.3 1.9-8.1 1.9c-10.9 0-19.7-8.9-19.7-19.7c0-7.5 4.3-14.4 9.8-19.5c9.4-8.8 22.2-26.4 22.2-56.7c0-53-43-96-96-96H224v64c0 12.6-7.4 24.1-19 29.2s-25 3-34.4-5.4l-160-144C3.9 225.7 0 217.1 0 208s3.9-17.7 10.6-23.8l160-144c9.4-8.5 22.9-10.6 34.4-5.4z"></path></svg></i>
                                </button>
                            </div>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-2 h-2 bg-primary rounded-full"></div>
                            <span class="text-xs text-gray-400">12 messages</span>
                        </div>
                    </div>
                    
                    <!-- Chat Item 2 -->
                    <div class="p-3 bg-gray-700/50 rounded-lg hover:bg-gray-700 cursor-pointer transition-colors">
                        <div class="flex items-start justify-between mb-2">
                            <div class="flex-1">
                                <h5 class="font-medium text-xs text-supplement1 mb-1">Design Token Updates</h5>
                                <p class="text-xs text-gray-400 line-clamp-2">We need to update the color tokens to match the new brand guidelines. Should we create a migration script?</p>
                            </div>
                            <div class="flex items-center gap-1 ml-2">
                                <span class="text-xs text-gray-500">4h ago</span>
                                <button class="p-1 hover:bg-gray-600 rounded transition-colors">
                                    <i class="text-gray-400 text-xs" data-fa-i2svg=""><svg class="svg-inline--fa fa-reply" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="reply" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M205 34.8c11.5 5.1 19 16.6 19 29.2v64H336c97.2 0 176 78.8 176 176c0 113.3-81.5 163.9-100.2 174.1c-2.5 1.4-5.3 1.9-8.1 1.9c-10.9 0-19.7-8.9-19.7-19.7c0-7.5 4.3-14.4 9.8-19.5c9.4-8.8 22.2-26.4 22.2-56.7c0-53-43-96-96-96H224v64c0 12.6-7.4 24.1-19 29.2s-25 3-34.4-5.4l-160-144C3.9 225.7 0 217.1 0 208s3.9-17.7 10.6-23.8l160-144c9.4-8.5 22.9-10.6 34.4-5.4z"></path></svg></i>
                                </button>
                            </div>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-2 h-2 bg-secondary rounded-full"></div>
                            <span class="text-xs text-gray-400">8 messages</span>
                        </div>
                    </div>
                    
                    <!-- Chat Item 3 -->
                    <div class="p-3 bg-gray-700/50 rounded-lg hover:bg-gray-700 cursor-pointer transition-colors">
                        <div class="flex items-start justify-between mb-2">
                            <div class="flex-1">
                                <h5 class="font-medium text-xs text-supplement1 mb-1">Button Component Review</h5>
                                <p class="text-xs text-gray-400 line-clamp-2">Let's review the button variants and make sure they're accessible across all themes.</p>
                            </div>
                            <div class="flex items-center gap-1 ml-2">
                                <span class="text-xs text-gray-500">1d ago</span>
                                <button class="p-1 hover:bg-gray-600 rounded transition-colors">
                                    <i class="text-gray-400 text-xs" data-fa-i2svg=""><svg class="svg-inline--fa fa-reply" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="reply" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M205 34.8c11.5 5.1 19 16.6 19 29.2v64H336c97.2 0 176 78.8 176 176c0 113.3-81.5 163.9-100.2 174.1c-2.5 1.4-5.3 1.9-8.1 1.9c-10.9 0-19.7-8.9-19.7-19.7c0-7.5 4.3-14.4 9.8-19.5c9.4-8.8 22.2-26.4 22.2-56.7c0-53-43-96-96-96H224v64c0 12.6-7.4 24.1-19 29.2s-25 3-34.4-5.4l-160-144C3.9 225.7 0 217.1 0 208s3.9-17.7 10.6-23.8l160-144c9.4-8.5 22.9-10.6 34.4-5.4z"></path></svg></i>
                                </button>
                            </div>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-2 h-2 bg-supplement2 rounded-full"></div>
                            <span class="text-xs text-gray-400">5 messages</span>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
        
        <!-- Recent Files Column -->
        <div class="w-1/2">
            <div class="p-3 border-b border-tertiary/30">
                <h4 class="text-xs font-medium text-supplement1">Recent Files</h4>
            </div>
            <div class="overflow-y-auto p-3 h-full">
                <div class="space-y-2">
                    
                    <!-- File Item 1 -->
                    <div class="p-3 bg-gray-700/50 rounded-lg hover:bg-gray-700 cursor-pointer transition-colors">
                        <div class="flex items-start justify-between mb-2">
                            <div class="flex items-center gap-2 flex-1">
                                <i class="text-primary text-sm" data-fa-i2svg=""><svg class="svg-inline--fa fa-file-lines" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="file-lines" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" data-fa-i2svg=""><path fill="currentColor" d="M64 0C28.7 0 0 28.7 0 64V448c0 35.3 28.7 64 64 64H320c35.3 0 64-28.7 64-64V160H256c-17.7 0-32-14.3-32-32V0H64zM256 0V128H384L256 0zM112 256H272c8.8 0 16 7.2 16 16s-7.2 16-16 16H112c-8.8 0-16-7.2-16-16s7.2-16 16-16zm0 64H272c8.8 0 16 7.2 16 16s-7.2 16-16 16H112c-8.8 0-16-7.2-16-16s7.2-16 16-16zm0 64H272c8.8 0 16 7.2 16 16s-7.2 16-16 16H112c-8.8 0-16-7.2-16-16s7.2-16 16-16z"></path></svg></i>
                                <div class="flex-1">
                                    <h5 class="font-medium text-xs text-supplement1 mb-1">master.md</h5>
                                    <p class="text-xs text-gray-400">Project Alpha Design System overview and getting started guide</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-1 ml-2">
                                <span class="text-xs text-gray-500">1h ago</span>
                                <button class="p-1 hover:bg-gray-600 rounded transition-colors">
                                    <i class="text-gray-400 text-xs" data-fa-i2svg=""><svg class="svg-inline--fa fa-up-right-from-square" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="up-right-from-square" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M352 0c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9L370.7 96 201.4 265.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L416 141.3l41.4 41.4c9.2 9.2 22.9 11.9 34.9 6.9s19.8-16.6 19.8-29.6V32c0-17.7-14.3-32-32-32H352zM80 32C35.8 32 0 67.8 0 112V432c0 44.2 35.8 80 80 80H400c44.2 0 80-35.8 80-80V320c0-17.7-14.3-32-32-32s-32 14.3-32 32V432c0 8.8-7.2 16-16 16H80c-8.8 0-16-7.2-16-16V112c0-8.8 7.2-16 16-16H192c17.7 0 32-14.3 32-32s-14.3-32-32-32H80z"></path></svg></i>
                                </button>
                            </div>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-2 h-2 bg-primary rounded-full"></div>
                            <span class="text-xs text-gray-400">Modified</span>
                        </div>
                    </div>
                    
                    <!-- File Item 2 -->
                    <div class="p-3 bg-gray-700/50 rounded-lg hover:bg-gray-700 cursor-pointer transition-colors">
                        <div class="flex items-start justify-between mb-2">
                            <div class="flex items-center gap-2 flex-1">
                                <i class="text-secondary text-sm" data-fa-i2svg=""><svg class="svg-inline--fa fa-file-lines" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="file-lines" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" data-fa-i2svg=""><path fill="currentColor" d="M64 0C28.7 0 0 28.7 0 64V448c0 35.3 28.7 64 64 64H320c35.3 0 64-28.7 64-64V160H256c-17.7 0-32-14.3-32-32V0H64zM256 0V128H384L256 0zM112 256H272c8.8 0 16 7.2 16 16s-7.2 16-16 16H112c-8.8 0-16-7.2-16-16s7.2-16 16-16zm0 64H272c8.8 0 16 7.2 16 16s-7.2 16-16 16H112c-8.8 0-16-7.2-16-16s7.2-16 16-16zm0 64H272c8.8 0 16 7.2 16 16s-7.2 16-16 16H112c-8.8 0-16-7.2-16-16s7.2-16 16-16z"></path></svg></i>
                                <div class="flex-1">
                                    <h5 class="font-medium text-xs text-supplement1 mb-1">buttons.md</h5>
                                    <p class="text-xs text-gray-400">Button component specifications, variants, and usage guidelines</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-1 ml-2">
                                <span class="text-xs text-gray-500">3h ago</span>
                                <button class="p-1 hover:bg-gray-600 rounded transition-colors">
                                    <i class="text-gray-400 text-xs" data-fa-i2svg=""><svg class="svg-inline--fa fa-up-right-from-square" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="up-right-from-square" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M352 0c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9L370.7 96 201.4 265.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L416 141.3l41.4 41.4c9.2 9.2 22.9 11.9 34.9 6.9s19.8-16.6 19.8-29.6V32c0-17.7-14.3-32-32-32H352zM80 32C35.8 32 0 67.8 0 112V432c0 44.2 35.8 80 80 80H400c44.2 0 80-35.8 80-80V320c0-17.7-14.3-32-32-32s-32 14.3-32 32V432c0 8.8-7.2 16-16 16H80c-8.8 0-16-7.2-16-16V112c0-8.8 7.2-16 16-16H192c17.7 0 32-14.3 32-32s-14.3-32-32-32H80z"></path></svg></i>
                                </button>
                            </div>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-2 h-2 bg-secondary rounded-full"></div>
                            <span class="text-xs text-gray-400">Modified</span>
                        </div>
                    </div>
                    
                    <!-- File Item 3 -->
                    <div class="p-3 bg-gray-700/50 rounded-lg hover:bg-gray-700 cursor-pointer transition-colors">
                        <div class="flex items-start justify-between mb-2">
                            <div class="flex items-center gap-2 flex-1">
                                <i class="text-gray-400 text-sm" data-fa-i2svg=""><svg class="svg-inline--fa fa-file-lines" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="file-lines" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" data-fa-i2svg=""><path fill="currentColor" d="M64 0C28.7 0 0 28.7 0 64V448c0 35.3 28.7 64 64 64H320c35.3 0 64-28.7 64-64V160H256c-17.7 0-32-14.3-32-32V0H64zM256 0V128H384L256 0zM112 256H272c8.8 0 16 7.2 16 16s-7.2 16-16 16H112c-8.8 0-16-7.2-16-16s7.2-16 16-16zm0 64H272c8.8 0 16 7.2 16 16s-7.2 16-16 16H112c-8.8 0-16-7.2-16-16s7.2-16 16-16zm0 64H272c8.8 0 16 7.2 16 16s-7.2 16-16 16H112c-8.8 0-16-7.2-16-16s7.2-16 16-16z"></path></svg></i>
                                <div class="flex-1">
                                    <h5 class="font-medium text-xs text-supplement1 mb-1">forms.md</h5>
                                    <p class="text-xs text-gray-400">Form components, input fields, validation patterns and accessibility</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-1 ml-2">
                                <span class="text-xs text-gray-500">2d ago</span>
                                <button class="p-1 hover:bg-gray-600 rounded transition-colors">
                                    <i class="text-gray-400 text-xs" data-fa-i2svg=""><svg class="svg-inline--fa fa-up-right-from-square" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="up-right-from-square" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M352 0c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9L370.7 96 201.4 265.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L416 141.3l41.4 41.4c9.2 9.2 22.9 11.9 34.9 6.9s19.8-16.6 19.8-29.6V32c0-17.7-14.3-32-32-32H352zM80 32C35.8 32 0 67.8 0 112V432c0 44.2 35.8 80 80 80H400c44.2 0 80-35.8 80-80V320c0-17.7-14.3-32-32-32s-32 14.3-32 32V432c0 8.8-7.2 16-16 16H80c-8.8 0-16-7.2-16-16V112c0-8.8 7.2-16 16-16H192c17.7 0 32-14.3 32-32s-14.3-32-32-32H80z"></path></svg></i>
                                </button>
                            </div>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
                            <span class="text-xs text-gray-400">Viewed</span>
                        </div>
                    </div>
                    
                    <!-- File Item 4 -->
                    <div class="p-3 bg-gray-700/50 rounded-lg hover:bg-gray-700 cursor-pointer transition-colors">
                        <div class="flex items-start justify-between mb-2">
                            <div class="flex items-center gap-2 flex-1">
                                <i class="text-supplement2 text-sm" data-fa-i2svg=""><svg class="svg-inline--fa fa-file-code" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="file-code" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" data-fa-i2svg=""><path fill="currentColor" d="M64 0C28.7 0 0 28.7 0 64V448c0 35.3 28.7 64 64 64H320c35.3 0 64-28.7 64-64V160H256c-17.7 0-32-14.3-32-32V0H64zM256 0V128H384L256 0zM153 289l-31 31 31 31c9.4 9.4 9.4 24.6 0 33.9s-24.6 9.4-33.9 0L71 337c-9.4-9.4-9.4-24.6 0-33.9l48-48c9.4-9.4 24.6-9.4 33.9 0s9.4 24.6 0 33.9zM265 255l48 48c9.4 9.4 9.4 24.6 0 33.9l-48 48c-9.4 9.4-24.6 9.4-33.9 0s-9.4-24.6 0-33.9l31-31-31-31c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0z"></path></svg></i>
                                <div class="flex-1">
                                    <h5 class="font-medium text-xs text-supplement1 mb-1">tokens.json</h5>
                                    <p class="text-xs text-gray-400">Design tokens configuration file with colors, spacing, and typography</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-1 ml-2">
                                <span class="text-xs text-gray-500">3d ago</span>
                                <button class="p-1 hover:bg-gray-600 rounded transition-colors">
                                    <i class="text-gray-400 text-xs" data-fa-i2svg=""><svg class="svg-inline--fa fa-up-right-from-square" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="up-right-from-square" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M352 0c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9L370.7 96 201.4 265.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L416 141.3l41.4 41.4c9.2 9.2 22.9 11.9 34.9 6.9s19.8-16.6 19.8-29.6V32c0-17.7-14.3-32-32-32H352zM80 32C35.8 32 0 67.8 0 112V432c0 44.2 35.8 80 80 80H400c44.2 0 80-35.8 80-80V320c0-17.7-14.3-32-32-32s-32 14.3-32 32V432c0 8.8-7.2 16-16 16H80c-8.8 0-16-7.2-16-16V112c0-8.8 7.2-16 16-16H192c17.7 0 32-14.3 32-32s-14.3-32-32-32H80z"></path></svg></i>
                                </button>
                            </div>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-2 h-2 bg-supplement2 rounded-full"></div>
                            <span class="text-xs text-gray-400">Added</span>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
        
    </div>
</div></div></div></div></body></html>