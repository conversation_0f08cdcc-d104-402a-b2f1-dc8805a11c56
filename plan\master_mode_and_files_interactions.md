# Master Mode & Files Interactions Implementation Plan

## Executive Vision

Master Mode represents the convergence of AI context and human context - a unified canvas where every human interaction becomes a signal of intent, driving local AI models as action engines. This plan unifies the mermaid viewer experience in FilePageOverlay with the master.md intelligence canvas, creating a seamless interaction paradigm.

---

## 1. Unified Canvas Architecture

### Core Principle: Intent-Driven Intelligence
Every human interaction (file open, text selection, annotation creation, navigation) is a **signal of intent**. The local AI model acts as an **action engine** that:
- Captures these collision points
- Processes them with limited but focused reasoning
- Generates actionable intelligence
- Feeds back into the context loop

### Canvas Unification Strategy

```typescript
// Unified Canvas Interface
interface MasterCanvas {
  mode: 'master_mode' | 'file_overlay' | 'hybrid';
  content_type: 'master_md' | 'mermaid_diagram' | 'document' | 'multi_content';
  interaction_surface: InteractionSurface;
  intelligence_layer: IntelligenceLayer;
  context_bridge: ContextBridge;
}
```

---

## 2. Master Mode Canvas Components

### A. Primary Canvas Types

#### 1. **Master.md Intelligence Canvas**
```typescript
// Master.md View in Master Mode
interface MasterModeCanvas {
  layout: 'temporal_timeline' | 'semantic_clusters' | 'context_pulse' | 'relationship_map';
  interaction_zones: {
    semantic_clusters: SemanticCluster[];    // Clickable topic bubbles
    temporal_insights: TemporalInsight[];    // Time-based patterns
    context_pulse: ContextPulse;             // Live activity feed
    quick_actions: QuickAction[];            // AI-suggested actions
  };
  ai_suggestions: AISuggestion[];
  human_annotations: HumanAnnotation[];
}
```

#### 2. **Enhanced Mermaid Viewer Canvas**
```typescript
// Mermaid Viewer with Intelligence Layer
interface MermaidIntelligenceCanvas {
  diagram_content: string;
  interactive_elements: {
    clickable_nodes: MermaidNode[];
    relationship_paths: RelationshipPath[];
    context_annotations: ContextAnnotation[];
    smart_suggestions: SmartSuggestion[];
  };
  intelligence_overlay: {
    related_files: RelatedFile[];
    pattern_insights: PatternInsight[];
    temporal_connections: TemporalConnection[];
  };
}
```

### B. Interaction Surface Design

#### **Unified Interaction Patterns**
```typescript
// Common interaction patterns across both canvases
interface InteractionSurface {
  click_actions: {
    single_click: 'select' | 'navigate' | 'expand';
    double_click: 'edit' | 'deep_dive' | 'annotate';
    right_click: 'context_menu' | 'smart_actions';
  };
  
  hover_intelligence: {
    show_connections: boolean;
    display_insights: boolean;
    preview_content: boolean;
  };
  
  selection_modes: {
    text_selection: 'annotate' | 'extract' | 'connect';
    area_selection: 'group' | 'analyze' | 'export';
    multi_selection: 'batch_operations' | 'comparison';
  };
}
```

---

## 3. Intelligence Layer Implementation

### A. Local AI as Action Engine

#### **Collision Point Detection**
```typescript
// Capture human-AI collision points
interface CollisionPoint {
  interaction_type: 'file_open' | 'text_select' | 'annotation_create' | 'navigation' | 'edit';
  timestamp: string;
  context_data: {
    current_file?: string;
    selected_content?: string;
    cursor_position?: Position;
    viewport_state?: ViewportState;
    related_files?: string[];
  };
  intent_signals: {
    urgency: 'low' | 'medium' | 'high';
    focus_depth: 'scanning' | 'reading' | 'analyzing' | 'creating';
    task_type: 'research' | 'implementation' | 'review' | 'planning';
  };
}
```

#### **AI Action Engine Response**
```typescript
// Local AI processes collision points
class AIActionEngine {
  processCollision(collision: CollisionPoint): AIResponse {
    return {
      immediate_actions: [
        'highlight_related_content',
        'suggest_connections',
        'preload_context'
      ],
      intelligence_insights: [
        'pattern_recognition',
        'temporal_correlation',
        'semantic_clustering'
      ],
      suggested_next_steps: [
        'recommended_files',
        'potential_annotations',
        'workflow_optimization'
      ]
    };
  }
}
```

### B. Context Bridge Between Canvases

#### **Seamless Context Flow**
```typescript
// Bridge context between master.md and file viewers
interface ContextBridge {
  active_context: {
    current_focus: string;           // What user is working on
    temporal_state: string;          // When in their workflow
    semantic_cluster: string;        // What topic area
    relationship_map: string[];      // Connected elements
  };
  
  context_transfer: {
    from_master_to_file: (context: MasterContext) => FileContext;
    from_file_to_master: (context: FileContext) => MasterContext;
    maintain_state: (transition: CanvasTransition) => void;
  };
  
  intelligence_sync: {
    annotations: SmartAnnotation[];
    insights: AIInsight[];
    patterns: UsagePattern[];
    connections: ContextConnection[];
  };
}
```

---

## 4. Master Mode UI/UX Design

### A. Canvas Layout System

#### **Adaptive Canvas Layout**
```html
<!-- Master Mode Canvas Container -->
<div class="master-mode-canvas" data-mode="master_intelligence">
  
  <!-- Intelligence Header -->
  <div class="intelligence-header">
    <div class="context-pulse">
      <span class="pulse-indicator active"></span>
      <span class="current-focus">API Design Session</span>
      <span class="ai-status">Processing...</span>
    </div>
    
    <div class="canvas-controls">
      <button class="toggle-layout" data-layout="temporal">Timeline</button>
      <button class="toggle-layout" data-layout="semantic">Clusters</button>
      <button class="toggle-layout" data-layout="relationship">Connections</button>
    </div>
  </div>
  
  <!-- Main Canvas Area -->
  <div class="canvas-main">
    
    <!-- Left: Context Navigation -->
    <div class="context-navigation">
      <div class="semantic-clusters">
        <!-- AI-generated topic bubbles -->
        <div class="cluster-bubble primary" data-confidence="0.92">
          <span class="cluster-name">API Design</span>
          <span class="cluster-count">5 files</span>
        </div>
      </div>
      
      <div class="temporal-insights">
        <!-- Time-based patterns -->
        <div class="insight-item">
          <span class="time-pattern">Morning Focus</span>
          <span class="pattern-confidence">High</span>
        </div>
      </div>
    </div>
    
    <!-- Center: Main Content -->
    <div class="canvas-content">
      <!-- Dynamic content based on mode -->
      <div class="master-md-view" v-if="mode === 'master'">
        <!-- Enhanced master.md with interactive elements -->
      </div>
      
      <div class="mermaid-intelligence-view" v-if="mode === 'mermaid'">
        <!-- Enhanced mermaid with AI overlay -->
      </div>
    </div>
    
    <!-- Right: AI Intelligence Panel -->
    <div class="ai-intelligence-panel">
      <div class="smart-suggestions">
        <!-- AI-generated insights and actions -->
      </div>
      
      <div class="context-connections">
        <!-- Related files and concepts -->
      </div>
    </div>
  </div>
  
  <!-- Intelligence Footer -->
  <div class="intelligence-footer">
    <div class="ai-insights-stream">
      <!-- Real-time AI insights as they generate -->
    </div>
  </div>
  
</div>
```

### B. Interactive Elements Design

#### **Smart Annotation Integration**
```typescript
// Unified annotation system across canvases
interface UnifiedAnnotationSystem {
  master_mode_annotations: {
    semantic_annotations: SemanticAnnotation[];    // Topic-based insights
    temporal_annotations: TemporalAnnotation[];    // Time-based patterns
    relationship_annotations: RelationshipAnnotation[]; // Connection insights
  };
  
  file_mode_annotations: {
    document_annotations: SmartAnnotation[];       // From multi-note system
    content_annotations: ContentAnnotation[];     // Text-specific
    ai_annotations: AIAnnotation[];               // AI-generated insights
  };
  
  cross_canvas_sync: {
    sync_annotations: (source: Canvas, target: Canvas) => void;
    merge_insights: (annotations: Annotation[]) => UnifiedInsight[];
    maintain_context: (transition: CanvasTransition) => void;
  };
}
```

---

## 5. Technical Implementation Strategy

### A. Enhanced Mermaid Viewer Module

#### **MermaidIntelligenceViewer Component**
```typescript
// Enhanced mermaid viewer with intelligence layer
export class MermaidIntelligenceViewer extends React.Component {
  state = {
    diagram_content: string;
    intelligence_overlay: IntelligenceOverlay;
    interaction_mode: 'view' | 'annotate' | 'connect' | 'analyze';
    ai_suggestions: AISuggestion[];
  };
  
  // Enhanced interaction handlers
  handleNodeClick = (node: MermaidNode) => {
    // Capture collision point
    const collision = this.captureCollision('node_click', { node });
    
    // Trigger AI analysis
    const aiResponse = AIActionEngine.processCollision(collision);
    
    // Update intelligence overlay
    this.updateIntelligenceOverlay(aiResponse);
    
    // Sync with master mode context
    ContextBridge.syncToMaster(aiResponse.context);
  };
  
  handleTextSelection = (selectedText: string, position: Position) => {
    // Similar collision processing for text selection
  };
  
  renderIntelligenceOverlay = () => {
    return (
      <div className="intelligence-overlay">
        {this.state.ai_suggestions.map(suggestion => (
          <AIInsightBubble key={suggestion.id} suggestion={suggestion} />
        ))}
      </div>
    );
  };
}
```

#### **Master Mode Canvas Component**
```typescript
// Master.md intelligent canvas
export class MasterModeCanvas extends React.Component {
  state = {
    layout_mode: 'semantic_clusters' | 'temporal_timeline' | 'relationship_map';
    active_clusters: SemanticCluster[];
    temporal_insights: TemporalInsight[];
    ai_intelligence: AIIntelligence;
    context_pulse: ContextPulse;
  };
  
  // Unified interaction handling
  handleClusterClick = (cluster: SemanticCluster) => {
    // Process intent signal
    const collision = this.captureCollision('cluster_select', { cluster });
    
    // Generate AI insights
    const insights = AIActionEngine.analyzeCluster(cluster, collision);
    
    // Update canvas state
    this.updateCanvasIntelligence(insights);
    
    // Sync with file viewers
    ContextBridge.syncToFileMode(insights.context);
  };
  
  renderSemanticClusters = () => {
    return this.state.active_clusters.map(cluster => (
      <InteractiveClusterBubble 
        key={cluster.id}
        cluster={cluster}
        onClick={this.handleClusterClick}
        onHover={this.handleClusterHover}
        intelligence={this.state.ai_intelligence}
      />
    ));
  };
}
```

### B. Intelligence Processing Pipeline

#### **Collision Point Processing**
```typescript
// Process human-AI collision points
class IntelligenceProcessor {
  
  // Process interaction signals
  static processCollision(collision: CollisionPoint): Promise<AIResponse> {
    return new Promise(async (resolve) => {
      
      // 1. Analyze intent signals
      const intent = await this.analyzeIntent(collision);
      
      // 2. Generate context insights
      const insights = await this.generateInsights(collision, intent);
      
      // 3. Suggest next actions
      const actions = await this.suggestActions(insights);
      
      // 4. Update intelligence state
      const response = {
        intent,
        insights,
        actions,
        context_updates: this.generateContextUpdates(collision, insights)
      };
      
      resolve(response);
    });
  }
  
  // Analyze user intent from interaction patterns
  static analyzeIntent(collision: CollisionPoint): IntentAnalysis {
    // Use local AI model to analyze intent
    // Pattern recognition based on interaction history
    // Return structured intent analysis
  }
  
  // Generate contextual insights
  static generateInsights(collision: CollisionPoint, intent: IntentAnalysis): AIInsight[] {
    // Process with local model
    // Generate semantic connections
    // Identify temporal patterns
    // Return structured insights
  }
}
```

---

## 6. Implementation Phases

### Phase 1: Canvas Unification (Week 1-2)
**Goal**: Create unified interaction patterns between master.md and mermaid viewer

**Deliverables**:
- [ ] Enhanced MermaidIntelligenceViewer component
- [ ] MasterModeCanvas component
- [ ] Unified InteractionSurface interface
- [ ] Basic ContextBridge implementation

**Key Features**:
- Consistent interaction patterns (click, hover, select)
- Basic context transfer between canvases
- Simple AI suggestion display
- Unified annotation system foundation

### Phase 2: Intelligence Layer (Week 3-4)
**Goal**: Implement AI action engine and collision point processing

**Deliverables**:
- [ ] AIActionEngine class
- [ ] CollisionPoint detection system
- [ ] IntelligenceProcessor pipeline
- [ ] Real-time insight generation

**Key Features**:
- Intent signal processing
- AI-generated insights and suggestions
- Pattern recognition from interactions
- Context-aware recommendations

### Phase 3: Master Mode Experience (Week 5-6)
**Goal**: Complete master mode canvas with full intelligence capabilities

**Deliverables**:
- [ ] Semantic cluster visualization
- [ ] Temporal insight display
- [ ] Relationship mapping
- [ ] Context pulse interface

**Key Features**:
- Interactive semantic bubbles
- Timeline-based insights
- Connection visualization
- Real-time AI collaboration

### Phase 4: Advanced Intelligence (Week 7-8)
**Goal**: Predictive capabilities and advanced AI collaboration

**Deliverables**:
- [ ] Predictive context loading
- [ ] Advanced pattern recognition
- [ ] Cross-canvas intelligence sync
- [ ] Performance optimization

**Key Features**:
- Proactive AI suggestions
- Workflow optimization
- Advanced relationship mapping
- Seamless context transitions

---

## 7. Success Metrics

### A. User Experience Metrics
- **Context Switch Time**: <2 seconds between canvases
- **Intent Recognition Accuracy**: >85% for common interactions
- **AI Suggestion Relevance**: >80% user acceptance rate
- **Workflow Efficiency**: 30% reduction in context searching time

### B. Technical Performance Metrics
- **Real-time Processing**: <500ms for collision point analysis
- **Memory Usage**: <100MB for intelligence layer
- **Responsiveness**: 60fps canvas interactions
- **AI Model Efficiency**: <2 seconds for insight generation

### C. Intelligence Quality Metrics
- **Pattern Recognition**: Detect 90% of user workflow patterns
- **Context Accuracy**: 85% accuracy in context suggestions
- **Relationship Mapping**: Identify 75% of semantic connections
- **Temporal Insights**: Predict user needs with 70% accuracy

---

## 8. Risk Mitigation

### A. Technical Risks
- **Performance**: Implement progressive loading and caching
- **Complexity**: Use modular architecture with clear interfaces
- **AI Model Limits**: Design graceful degradation for model failures
- **Memory Usage**: Implement tiered storage and cleanup strategies

### B. User Experience Risks
- **Learning Curve**: Provide intuitive defaults and guided tours
- **Overwhelming Information**: Implement progressive disclosure
- **Context Loss**: Ensure robust state management and recovery
- **AI Reliability**: Provide manual override for all AI suggestions

---

## Conclusion

This implementation plan creates a unified Master Mode canvas that serves as the centerpiece for AI-human context collision. By treating every interaction as an intent signal and using local AI as an action engine, we create a responsive, intelligent workspace that amplifies human creativity while maintaining local control and privacy.

The unified canvas approach ensures that whether users are working with master.md intelligence clusters or analyzing mermaid diagrams, they have consistent, powerful tools for context discovery and knowledge connection. This positions Chatlo as a truly intelligent context companion rather than just another chat application.

---

*Implementation Plan Version: 1.0*  
*Created: January 27, 2025*  
*Next Review: February 10, 2025*