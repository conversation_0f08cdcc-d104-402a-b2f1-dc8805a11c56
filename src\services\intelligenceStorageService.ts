/**
 * Intelligence Storage Service
 * Manages JSON storage for file-level and vault-level intelligence data
 */

import { BaseService, ServiceError, ServiceErrorCode } from './base'
import { 
  FileIntelligence, 
  VaultIntelligence, 
  KeyIdea, 
  WeightedEntity, 
  HumanConnection,
  ProcessingResult 
} from '../types/fileIntelligenceTypes'
// Note: crypto import removed for browser compatibility

export interface IntelligenceStorageOptions {
  vaultPath: string
  createDirectories?: boolean
}

class IntelligenceStorageService extends BaseService {
  private readonly CONTEXT_DIR = '.context'
  private readonly FILES_DIR = 'files'
  private readonly VAULT_INTELLIGENCE_FILE = 'vault_intelligence.json'

  constructor() {
    super({
      name: 'IntelligenceStorageService',
      autoInitialize: true
    })
  }

  protected async doInitialize(): Promise<void> {
    this.logger.info('Intelligence Storage Service initialized', 'doInitialize')
  }

  /**
   * Store file intelligence data
   */
  async storeFileIntelligence(
    filePath: string, 
    intelligence: FileIntelligence, 
    options: IntelligenceStorageOptions
  ): Promise<void> {
    return await this.executeOperationOrThrow(
      'storeFileIntelligence',
      async () => {
        this.logger.info('Storing file intelligence', 'storeFileIntelligence', { filePath })

        // Generate file hash for unique identification
        const fileHash = this.generateFileHash(filePath)
        
        // Create storage path
        const contextDir = this.getContextDirectory(options.vaultPath)
        const filesDir = this.getFilesDirectory(options.vaultPath)
        const intelligenceFile = `${filesDir}/${fileHash}.json`

        // Ensure directories exist
        if (options.createDirectories) {
          await this.ensureDirectoryExists(contextDir)
          await this.ensureDirectoryExists(filesDir)
        }

        // Prepare intelligence data with metadata
        const intelligenceData = {
          ...intelligence,
          storage_metadata: {
            file_path: filePath,
            file_hash: fileHash,
            stored_at: new Date().toISOString(),
            storage_version: '1.0'
          }
        }

        // Write to file
        await this.writeJsonFile(intelligenceFile, intelligenceData)

        this.logger.info('File intelligence stored successfully', 'storeFileIntelligence', {
          filePath,
          intelligenceFile,
          ideasCount: intelligence.key_ideas.length,
          entitiesCount: intelligence.weighted_entities.length
        })
      },
      { filePath, vaultPath: options.vaultPath }
    )
  }

  /**
   * Retrieve file intelligence data
   */
  async getFileIntelligence(filePath: string, vaultPath: string): Promise<FileIntelligence | null> {
    return await this.executeOperationOrThrow(
      'getFileIntelligence',
      async () => {
        const fileHash = this.generateFileHash(filePath)
        const filesDir = this.getFilesDirectory(vaultPath)
        const intelligenceFile = `${filesDir}/${fileHash}.json`

        try {
          const data = await this.readJsonFile(intelligenceFile)
          
          if (data && this.isValidFileIntelligence(data)) {
            this.logger.info('File intelligence retrieved', 'getFileIntelligence', { filePath })
            return data as FileIntelligence
          }

          return null
        } catch (error) {
          // File doesn't exist or is invalid
          this.logger.debug('File intelligence not found', 'getFileIntelligence', { filePath })
          return null
        }
      },
      { filePath, vaultPath }
    )
  }

  /**
   * Store vault-level intelligence aggregation
   */
  async storeVaultIntelligence(
    vaultPath: string, 
    intelligence: VaultIntelligence
  ): Promise<void> {
    return await this.executeOperationOrThrow(
      'storeVaultIntelligence',
      async () => {
        this.logger.info('Storing vault intelligence', 'storeVaultIntelligence', { vaultPath })

        const contextDir = this.getContextDirectory(vaultPath)
        const intelligenceFile = `${contextDir}/${this.VAULT_INTELLIGENCE_FILE}`

        // Ensure directory exists
        await this.ensureDirectoryExists(contextDir)

        // Prepare intelligence data with metadata
        const intelligenceData = {
          ...intelligence,
          storage_metadata: {
            vault_path: vaultPath,
            stored_at: new Date().toISOString(),
            storage_version: '1.0'
          }
        }

        // Write to file
        await this.writeJsonFile(intelligenceFile, intelligenceData)

        this.logger.info('Vault intelligence stored successfully', 'storeVaultIntelligence', {
          vaultPath,
          totalFiles: intelligence.total_files_processed,
          totalIdeas: intelligence.aggregated_ideas.length
        })
      },
      { vaultPath }
    )
  }

  /**
   * Retrieve vault intelligence data
   */
  async getVaultIntelligence(vaultPath: string): Promise<VaultIntelligence | null> {
    return await this.executeOperationOrThrow(
      'getVaultIntelligence',
      async () => {
        const contextDir = this.getContextDirectory(vaultPath)
        const intelligenceFile = `${contextDir}/${this.VAULT_INTELLIGENCE_FILE}`

        try {
          const data = await this.readJsonFile(intelligenceFile)
          
          if (data && this.isValidVaultIntelligence(data)) {
            this.logger.info('Vault intelligence retrieved', 'getVaultIntelligence', { vaultPath })
            return data as VaultIntelligence
          }

          return null
        } catch (error) {
          this.logger.debug('Vault intelligence not found', 'getVaultIntelligence', { vaultPath })
          return null
        }
      },
      { vaultPath }
    )
  }

  /**
   * Update vault intelligence incrementally
   */
  async updateVaultIntelligence(
    vaultPath: string, 
    newFileIntelligence: FileIntelligence,
    filePath: string
  ): Promise<void> {
    return await this.executeOperationOrThrow(
      'updateVaultIntelligence',
      async () => {
        // Get existing vault intelligence
        let vaultIntelligence = await this.getVaultIntelligence(vaultPath)

        if (!vaultIntelligence) {
          // Create new vault intelligence
          vaultIntelligence = {
            vault_path: vaultPath,
            total_files_processed: 0,
            last_updated: new Date().toISOString(),
            aggregated_ideas: [],
            top_entities: [],
            human_connections: [],
            processing_summary: {
              total_processing_time_ms: 0,
              average_confidence: 0,
              models_used: [],
              last_full_scan: new Date().toISOString()
            }
          }
        }

        // Update with new file data
        vaultIntelligence.total_files_processed += 1
        vaultIntelligence.last_updated = new Date().toISOString()

        // Merge key ideas (avoid duplicates)
        const existingIdeaTexts = new Set(vaultIntelligence.aggregated_ideas.map(idea => idea.text.toLowerCase()))
        const newIdeas = newFileIntelligence.key_ideas.filter(idea => 
          !existingIdeaTexts.has(idea.text.toLowerCase())
        )
        vaultIntelligence.aggregated_ideas.push(...newIdeas)

        // Sort by relevance and keep top 50
        vaultIntelligence.aggregated_ideas.sort((a, b) => b.relevance_score - a.relevance_score)
        vaultIntelligence.aggregated_ideas = vaultIntelligence.aggregated_ideas.slice(0, 50)

        // Merge entities (avoid duplicates)
        const existingEntityTexts = new Set(vaultIntelligence.top_entities.map(entity => entity.text.toLowerCase()))
        const newEntities = newFileIntelligence.weighted_entities.filter(entity => 
          !existingEntityTexts.has(entity.text.toLowerCase())
        )
        vaultIntelligence.top_entities.push(...newEntities)

        // Sort by weight and keep top 30
        vaultIntelligence.top_entities.sort((a, b) => b.weight - a.weight)
        vaultIntelligence.top_entities = vaultIntelligence.top_entities.slice(0, 30)

        // Merge human connections (avoid duplicates)
        const existingConnections = new Set(vaultIntelligence.human_connections.map(conn => 
          `${conn.name?.toLowerCase()}_${conn.email?.toLowerCase()}_${conn.company?.toLowerCase()}`
        ))
        const newConnections = newFileIntelligence.human_connections.filter(conn => {
          const key = `${conn.name?.toLowerCase()}_${conn.email?.toLowerCase()}_${conn.company?.toLowerCase()}`
          return !existingConnections.has(key)
        })
        vaultIntelligence.human_connections.push(...newConnections)

        // Sort by confidence and keep top 20
        vaultIntelligence.human_connections.sort((a, b) => b.connection_strength - a.connection_strength)
        vaultIntelligence.human_connections = vaultIntelligence.human_connections.slice(0, 20)

        // Update processing summary
        vaultIntelligence.processing_summary.total_processing_time_ms += 
          newFileIntelligence.analysis_metadata.processing_time_ms

        const modelUsed = newFileIntelligence.analysis_metadata.model_used
        if (modelUsed && !vaultIntelligence.processing_summary.models_used.includes(modelUsed)) {
          vaultIntelligence.processing_summary.models_used.push(modelUsed)
        }

        // Recalculate average confidence
        vaultIntelligence.processing_summary.average_confidence = 
          (vaultIntelligence.processing_summary.average_confidence * (vaultIntelligence.total_files_processed - 1) + 
           newFileIntelligence.processing_confidence) / vaultIntelligence.total_files_processed

        // Store updated vault intelligence
        await this.storeVaultIntelligence(vaultPath, vaultIntelligence)

        this.logger.info('Vault intelligence updated', 'updateVaultIntelligence', {
          vaultPath,
          filePath,
          totalFiles: vaultIntelligence.total_files_processed,
          totalIdeas: vaultIntelligence.aggregated_ideas.length
        })
      },
      { vaultPath, filePath }
    )
  }

  /**
   * Generate file hash for unique identification
   */
  private generateFileHash(filePath: string): string {
    // Simple hash function for browser compatibility
    const combined = filePath + Date.now().toString()
    let hash = 0
    for (let i = 0; i < combined.length; i++) {
      const char = combined.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(16).substring(0, 16)
  }

  /**
   * Get context directory path
   */
  private getContextDirectory(vaultPath: string): string {
    return `${vaultPath}/${this.CONTEXT_DIR}`
  }

  /**
   * Get files directory path
   */
  private getFilesDirectory(vaultPath: string): string {
    return `${vaultPath}/${this.CONTEXT_DIR}/${this.FILES_DIR}`
  }

  /**
   * Ensure directory exists
   */
  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      if (window.electronAPI?.vault?.createDirectory) {
        const result = await window.electronAPI.vault.createDirectory(dirPath)
        if (!result.success) {
          throw new Error(`Failed to create directory: ${result.error}`)
        }
      }
    } catch (error) {
      this.logger.error('Failed to ensure directory exists', 'ensureDirectoryExists', { dirPath, error })
      throw error
    }
  }

  /**
   * Write JSON data to file
   */
  private async writeJsonFile(filePath: string, data: any): Promise<void> {
    try {
      if (window.electronAPI?.vault?.writeFile) {
        // Use proper UTF-8 encoding for international characters
        const jsonContent = JSON.stringify(data, null, 2)
          .replace(/\\u[\dA-F]{4}/gi, (match) => {
            return String.fromCharCode(parseInt(match.replace(/\\u/g, ''), 16))
          })
        const result = await window.electronAPI.vault.writeFile(filePath, jsonContent)
        if (!result.success) {
          throw new Error(`Failed to write file: ${result.error}`)
        }
      }
    } catch (error) {
      this.logger.error('Failed to write JSON file', 'writeJsonFile', { filePath, error })
      throw error
    }
  }

  /**
   * Read JSON data from file
   */
  private async readJsonFile(filePath: string): Promise<any> {
    try {
      if (window.electronAPI?.vault?.readFile) {
        const result = await window.electronAPI.vault.readFile(filePath)
        if (!result.success) {
          // File doesn't exist - return null instead of throwing
          if (result.error?.includes('ENOENT') || result.error?.includes('no such file')) {
            this.logger.info('JSON file does not exist', 'readJsonFile', { filePath })
            return null
          }
          throw new Error(`Failed to read file: ${result.error}`)
        }

        // Handle empty or invalid content
        if (!result.content || result.content.trim() === '') {
          this.logger.warn('JSON file is empty', 'readJsonFile', { filePath })
          return null
        }

        try {
          return JSON.parse(result.content)
        } catch (parseError) {
          this.logger.warn('Corrupted JSON file detected - auto-cleaning', 'readJsonFile', { filePath, parseError })
          // Auto-delete corrupted meta files since they can be regenerated
          await this.deleteCorruptedFile(filePath)
          return null
        }
      }
      return null
    } catch (error) {
      this.logger.error('Failed to read JSON file', 'readJsonFile', { filePath, error })
      throw error
    }
  }

  /**
   * Auto-delete corrupted meta files since they can be regenerated
   */
  private async deleteCorruptedFile(filePath: string): Promise<void> {
    console.log('🗑️ [DELETE-FILE] Starting deleteCorruptedFile for:', filePath)

    try {
      console.log('🔧 [DELETE-FILE] Checking electronAPI availability...')
      if (window.electronAPI?.vault?.removeFile) {
        console.log('✅ [DELETE-FILE] electronAPI.vault.removeFile is available')
        console.log('🗑️ [DELETE-FILE] Attempting to remove file...')

        const result = await window.electronAPI.vault.removeFile(filePath)
        console.log('📊 [DELETE-FILE] Remove file result:', result)

        if (result.success) {
          console.log('✅ [DELETE-FILE] File removed successfully')
          this.logger.info('Corrupted meta file deleted successfully', 'deleteCorruptedFile', { filePath })
        } else {
          console.log('⚠️ [DELETE-FILE] Remove file returned success=false:', result)
          this.logger.warn('Failed to delete corrupted file', 'deleteCorruptedFile', { filePath, error: result.error })
        }
      } else {
        console.log('❌ [DELETE-FILE] electronAPI.vault.removeFile is NOT available')
        console.log('🔍 [DELETE-FILE] Available electronAPI methods:', {
          electronAPI: !!window.electronAPI,
          vault: !!window.electronAPI?.vault,
          removeFile: !!window.electronAPI?.vault?.removeFile,
          deleteFile: !!window.electronAPI?.vault?.deleteFile
        })
      }
    } catch (error) {
      console.error('💥 [DELETE-FILE] Error during file deletion:', error)
      this.logger.warn('Error during corrupted file cleanup', 'deleteCorruptedFile', { filePath, error })
      // Don't throw - cleanup is best effort
    }

    console.log('🏁 [DELETE-FILE] deleteCorruptedFile completed for:', filePath)
  }

  /**
   * Clear all intelligence meta files in a vault (nuclear option for corrupted state)
   */
  async clearVaultIntelligenceState(vaultPath: string): Promise<void> {
    console.log('🗑️ [CLEAR-SERVICE] Starting clearVaultIntelligenceState for:', vaultPath)

    return await this.executeOperationOrThrow(
      'clearVaultIntelligenceState',
      async () => {
        console.log('🔍 [CLEAR-SERVICE] Getting context directory...')
        const contextDir = this.getContextDirectory(vaultPath)
        console.log('📁 [CLEAR-SERVICE] Context directory:', contextDir)

        // Delete vault intelligence file
        const vaultIntelligenceFile = `${contextDir}/${this.VAULT_INTELLIGENCE_FILE}`
        console.log('🎯 [CLEAR-SERVICE] Target vault intelligence file:', vaultIntelligenceFile)

        console.log('🗑️ [CLEAR-SERVICE] Attempting to delete vault intelligence file...')
        await this.deleteCorruptedFile(vaultIntelligenceFile)
        console.log('✅ [CLEAR-SERVICE] Vault intelligence file deletion completed')

        // Delete files directory if it exists
        const filesDir = `${contextDir}/files`
        console.log('🎯 [CLEAR-SERVICE] Target files directory:', filesDir)

        try {
          console.log('🔧 [CLEAR-SERVICE] Checking electronAPI availability...')
          if (window.electronAPI?.vault?.removeDirectory) {
            console.log('✅ [CLEAR-SERVICE] electronAPI.vault.removeDirectory is available')
            console.log('🗑️ [CLEAR-SERVICE] Attempting to remove files directory...')

            const result = await window.electronAPI.vault.removeDirectory(filesDir)
            console.log('📊 [CLEAR-SERVICE] Remove directory result:', result)

            if (result.success) {
              console.log('✅ [CLEAR-SERVICE] Files directory removed successfully')
              this.logger.info('Cleared files intelligence directory', 'clearVaultIntelligenceState', { filesDir })
            } else {
              console.log('⚠️ [CLEAR-SERVICE] Remove directory returned success=false:', result)
            }
          } else {
            console.log('❌ [CLEAR-SERVICE] electronAPI.vault.removeDirectory is NOT available')
            console.log('🔍 [CLEAR-SERVICE] Available electronAPI methods:', {
              electronAPI: !!window.electronAPI,
              vault: !!window.electronAPI?.vault,
              removeDirectory: !!window.electronAPI?.vault?.removeDirectory,
              deleteDirectory: !!window.electronAPI?.vault?.deleteDirectory
            })
          }
        } catch (error) {
          console.error('💥 [CLEAR-SERVICE] Error during files directory cleanup:', error)
          this.logger.debug('Files directory cleanup skipped', 'clearVaultIntelligenceState', { filesDir, error })
        }

        console.log('🎉 [CLEAR-SERVICE] Vault intelligence state clearing completed')
        this.logger.info('Vault intelligence state cleared', 'clearVaultIntelligenceState', { vaultPath })
      },
      { vaultPath }
    )
  }

  /**
   * Validate file intelligence data structure
   */
  private isValidFileIntelligence(data: any): boolean {
    return data && 
           Array.isArray(data.key_ideas) &&
           Array.isArray(data.weighted_entities) &&
           Array.isArray(data.human_connections) &&
           typeof data.processing_confidence === 'number' &&
           data.analysis_metadata
  }

  /**
   * Validate vault intelligence data structure
   */
  private isValidVaultIntelligence(data: any): boolean {
    return data &&
           typeof data.vault_path === 'string' &&
           typeof data.total_files_processed === 'number' &&
           Array.isArray(data.aggregated_ideas) &&
           Array.isArray(data.top_entities) &&
           Array.isArray(data.human_connections) &&
           data.processing_summary
  }
}

export const intelligenceStorageService = new IntelligenceStorageService()
