/**
 * Cache Analytics Service
 * Provides performance analytics, optimization recommendations, and cache health monitoring
 * Helps users understand and optimize their cache performance
 */

import { BaseService } from './base'
import { cacheManager } from './cacheManager'
import { performanceMonitor } from './performanceMonitor'

interface CacheMetrics {
  hitRate: number
  missRate: number
  evictionRate: number
  averageAccessTime: number
  hotCacheUtilization: number
  warmCacheUtilization: number
  coldCacheUtilization: number
  totalCacheSize: number
  memoryEfficiency: number
}

interface CacheRecommendation {
  type: 'performance' | 'memory' | 'storage' | 'configuration'
  priority: 'low' | 'medium' | 'high' | 'critical'
  title: string
  description: string
  action: string
  estimatedImpact: string
}

interface CacheHealthReport {
  overall: 'excellent' | 'good' | 'fair' | 'poor' | 'critical'
  metrics: CacheMetrics
  recommendations: CacheRecommendation[]
  systemOptimization: {
    currentTier: string
    optimalConfiguration: any
    performanceGains: string[]
  }
  timestamp: string
}

class CacheAnalytics extends BaseService {
  private metricsHistory: CacheMetrics[] = []
  private readonly MAX_HISTORY = 100
  private lastAnalysisTime = 0
  private readonly ANALYSIS_INTERVAL = 5 * 60 * 1000 // 5 minutes

  constructor() {
    super({
      name: 'CacheAnalytics',
      autoInitialize: true
    })
  }

  protected async doInitialize(): Promise<void> {
    // Start periodic analysis
    setInterval(() => this.performPeriodicAnalysis(), this.ANALYSIS_INTERVAL)
    
    this.logger.info('Cache analytics initialized', 'doInitialize')
  }

  protected async doHealthCheck(): Promise<boolean> {
    try {
      // Check if we can collect metrics
      const stats = await cacheManager.getStats()
      return stats !== null
    } catch (error) {
      return false
    }
  }

  /**
   * Generate comprehensive cache health report
   */
  async generateHealthReport(): Promise<CacheHealthReport> {
    try {
      const metrics = await this.collectCurrentMetrics()
      const recommendations = await this.generateRecommendations(metrics)
      const systemOptimization = await this.analyzeSystemOptimization()
      
      const overall = this.calculateOverallHealth(metrics, recommendations)

      const report: CacheHealthReport = {
        overall,
        metrics,
        recommendations,
        systemOptimization,
        timestamp: new Date().toISOString()
      }

      this.logger.info('Cache health report generated', 'generateHealthReport', {
        overall,
        recommendationCount: recommendations.length,
        hitRate: `${metrics.hitRate.toFixed(1)}%`
      })

      return report
    } catch (error) {
      this.logger.error('Failed to generate health report', 'generateHealthReport', error)
      throw error
    }
  }

  /**
   * Get cache performance trends over time
   */
  getPerformanceTrends(): {
    hitRateTrend: number[]
    memoryUsageTrend: number[]
    evictionTrend: number[]
    timeLabels: string[]
  } {
    const recentMetrics = this.metricsHistory.slice(-20) // Last 20 data points
    
    return {
      hitRateTrend: recentMetrics.map(m => m.hitRate),
      memoryUsageTrend: recentMetrics.map(m => m.hotCacheUtilization),
      evictionTrend: recentMetrics.map(m => m.evictionRate),
      timeLabels: recentMetrics.map((_, i) => `${i * 5}min ago`)
    }
  }

  /**
   * Get real-time cache statistics
   */
  async getRealTimeStats(): Promise<{
    currentMetrics: CacheMetrics
    systemStatus: any
    quickRecommendations: string[]
  }> {
    const currentMetrics = await this.collectCurrentMetrics()
    const systemProfile = performanceMonitor.getSystemProfile()
    const quickRecommendations = await this.getQuickRecommendations(currentMetrics)

    return {
      currentMetrics,
      systemStatus: {
        tier: systemProfile?.performanceTier || 'unknown',
        totalRAM: systemProfile ? `${Math.round(systemProfile.totalRAM / 1024 / 1024 / 1024)}GB` : 'unknown',
        meetsMinSpec: systemProfile?.meetsMinimumSpec || false
      },
      quickRecommendations
    }
  }

  /**
   * Optimize cache configuration based on usage patterns
   */
  async optimizeCacheConfiguration(): Promise<{
    currentConfig: any
    recommendedConfig: any
    expectedImprovements: string[]
  }> {
    const metrics = await this.collectCurrentMetrics()
    const systemProfile = performanceMonitor.getSystemProfile()
    
    // Analyze current configuration
    const currentConfig = {
      hotCacheSize: '256MB', // This would come from cache manager
      warmCacheSize: '1GB',
      coldCacheSize: '4GB'
    }

    // Generate optimized configuration
    const recommendedConfig = this.calculateOptimalConfiguration(metrics, systemProfile)
    
    // Estimate improvements
    const expectedImprovements = this.estimateConfigurationImprovements(metrics, recommendedConfig)

    return {
      currentConfig,
      recommendedConfig,
      expectedImprovements
    }
  }

  // Private helper methods
  private async collectCurrentMetrics(): Promise<CacheMetrics> {
    const stats = await cacheManager.getStats()
    const systemMemory = performanceMonitor.getSystemMemoryUsage()
    
    const metrics: CacheMetrics = {
      hitRate: stats.hitRate,
      missRate: 100 - stats.hitRate,
      evictionRate: this.calculateEvictionRate(stats),
      averageAccessTime: this.estimateAverageAccessTime(),
      hotCacheUtilization: (stats.hotCacheSize / (256 * 1024 * 1024)) * 100, // Assuming 256MB limit
      warmCacheUtilization: (stats.warmCacheSize / (1024 * 1024 * 1024)) * 100, // Assuming 1GB limit
      coldCacheUtilization: (stats.coldCacheSize / (4 * 1024 * 1024 * 1024)) * 100, // Assuming 4GB limit
      totalCacheSize: stats.hotCacheSize + stats.warmCacheSize + stats.coldCacheSize,
      memoryEfficiency: this.calculateMemoryEfficiency(stats, systemMemory)
    }

    // Store in history
    this.metricsHistory.push(metrics)
    if (this.metricsHistory.length > this.MAX_HISTORY) {
      this.metricsHistory.shift()
    }

    return metrics
  }

  private async generateRecommendations(metrics: CacheMetrics): Promise<CacheRecommendation[]> {
    const recommendations: CacheRecommendation[] = []

    // Hit rate recommendations
    if (metrics.hitRate < 50) {
      recommendations.push({
        type: 'performance',
        priority: 'high',
        title: 'Low Cache Hit Rate',
        description: `Current hit rate is ${metrics.hitRate.toFixed(1)}%, which is below optimal (>80%)`,
        action: 'Increase cache sizes or review file access patterns',
        estimatedImpact: 'Up to 3x faster file access'
      })
    }

    // Memory utilization recommendations
    if (metrics.hotCacheUtilization > 90) {
      recommendations.push({
        type: 'memory',
        priority: 'medium',
        title: 'Hot Cache Nearly Full',
        description: 'Memory cache is at 90%+ capacity, may cause frequent evictions',
        action: 'Increase hot cache size or optimize data structures',
        estimatedImpact: 'Reduced evictions, better performance'
      })
    }

    // System-specific recommendations
    const systemProfile = performanceMonitor.getSystemProfile()
    if (systemProfile?.performanceTier === 'high-end' && metrics.hotCacheUtilization < 30) {
      recommendations.push({
        type: 'configuration',
        priority: 'medium',
        title: 'Underutilized High-End System',
        description: 'Your system can handle larger cache sizes for better performance',
        action: 'Increase cache limits to utilize available RAM',
        estimatedImpact: 'Significantly faster file processing'
      })
    }

    return recommendations
  }

  private async analyzeSystemOptimization(): Promise<any> {
    const systemProfile = performanceMonitor.getSystemProfile()
    
    return {
      currentTier: systemProfile?.performanceTier || 'unknown',
      optimalConfiguration: this.calculateOptimalConfiguration(await this.collectCurrentMetrics(), systemProfile),
      performanceGains: [
        'Adaptive memory thresholds based on your hardware',
        'Intelligent cache sizing for your system tier',
        'Optimized processing limits for your CPU/RAM combination'
      ]
    }
  }

  private calculateOverallHealth(metrics: CacheMetrics, recommendations: CacheRecommendation[]): 'excellent' | 'good' | 'fair' | 'poor' | 'critical' {
    let score = 100

    // Deduct points for low hit rate
    if (metrics.hitRate < 80) score -= 20
    if (metrics.hitRate < 60) score -= 20
    if (metrics.hitRate < 40) score -= 20

    // Deduct points for high eviction rate
    if (metrics.evictionRate > 10) score -= 15

    // Deduct points for critical recommendations
    const criticalRecs = recommendations.filter(r => r.priority === 'critical')
    score -= criticalRecs.length * 25

    if (score >= 90) return 'excellent'
    if (score >= 75) return 'good'
    if (score >= 60) return 'fair'
    if (score >= 40) return 'poor'
    return 'critical'
  }

  private calculateEvictionRate(stats: any): number {
    // Calculate eviction rate based on stats
    return stats.evictions || 0
  }

  private estimateAverageAccessTime(): number {
    // Estimate based on cache tier access times
    return 50 // milliseconds (placeholder)
  }

  private calculateMemoryEfficiency(stats: any, systemMemory: any): number {
    // Calculate how efficiently we're using memory
    const cacheMemoryUsage = stats.hotCacheSize
    const totalSystemMemory = systemMemory.total
    return (cacheMemoryUsage / totalSystemMemory) * 100
  }

  private calculateOptimalConfiguration(metrics: CacheMetrics, systemProfile: any): any {
    if (!systemProfile) return {}

    const ramGB = Math.round(systemProfile.totalRAM / 1024 / 1024 / 1024)
    
    switch (systemProfile.performanceTier) {
      case 'minimum':
        return {
          hotCacheSize: '128MB',
          warmCacheSize: '512MB',
          coldCacheSize: '2GB',
          jsHeapLimit: '256MB'
        }
      case 'mid-range':
        return {
          hotCacheSize: '256MB',
          warmCacheSize: '1GB',
          coldCacheSize: '4GB',
          jsHeapLimit: '512MB'
        }
      case 'high-end':
        return {
          hotCacheSize: '512MB',
          warmCacheSize: '2GB',
          coldCacheSize: '8GB',
          jsHeapLimit: '1GB'
        }
      default:
        return {}
    }
  }

  private estimateConfigurationImprovements(metrics: CacheMetrics, recommendedConfig: any): string[] {
    const improvements = []
    
    if (metrics.hitRate < 70) {
      improvements.push('Hit rate improvement: +20-40%')
    }
    
    if (metrics.evictionRate > 5) {
      improvements.push('Reduced cache evictions: -50%')
    }
    
    improvements.push('Faster file access: 2-5x speed improvement')
    improvements.push('Better memory utilization')
    
    return improvements
  }

  private async getQuickRecommendations(metrics: CacheMetrics): Promise<string[]> {
    const quick = []
    
    if (metrics.hitRate < 60) {
      quick.push('Consider increasing cache sizes')
    }
    
    if (metrics.hotCacheUtilization > 85) {
      quick.push('Hot cache is nearly full')
    }
    
    if (metrics.memoryEfficiency < 5) {
      quick.push('System has unused memory capacity')
    }
    
    return quick
  }

  private async performPeriodicAnalysis(): Promise<void> {
    try {
      const now = Date.now()
      if (now - this.lastAnalysisTime < this.ANALYSIS_INTERVAL) return
      
      const metrics = await this.collectCurrentMetrics()
      this.lastAnalysisTime = now
      
      // Log periodic insights
      console.log(`📊 [CACHE-ANALYTICS] Hit Rate: ${metrics.hitRate.toFixed(1)}%, Memory: ${metrics.hotCacheUtilization.toFixed(1)}%`)
      
    } catch (error) {
      this.logger.error('Periodic analysis failed', 'performPeriodicAnalysis', error)
    }
  }
}

export const cacheAnalytics = new CacheAnalytics()
