import React, { useState, useEffect, useRef } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { ICONS } from './Icons/index'
import { pdfViewerService, PDFViewerState } from '../services/PDFViewerService'
import * as pdfjsLib from 'pdfjs-dist'

// Set up PDF.js worker - use local copy in public directory
pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.js'

interface PDFViewerOverlayProps {
  onClose: () => void
}

export const PDFViewerOverlay: React.FC<PDFViewerOverlayProps> = ({ onClose }) => {
  const [state, setState] = useState<PDFViewerState>(pdfViewerService.getState())
  const [pdfDocument, setPdfDocument] = useState<any>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(0)
  const [isLoading, setIsLoading] = useState(false)
  const canvasRef = useRef<HTMLCanvasElement>(null)

  // Subscribe to PDF viewer service state changes
  useEffect(() => {
    const unsubscribe = pdfViewerService.subscribe(setState)
    return unsubscribe
  }, [])

  // Load PDF content when file path changes
  useEffect(() => {
    console.log('PDF useEffect triggered:', { filePath: state.filePath, isOpen: state.isOpen })
    if (state.filePath && state.isOpen) {
      console.log('Calling loadPDFContent...')
      loadPDFContent()
    }
  }, [state.filePath, state.isOpen])

  const loadPDFContent = async () => {
    if (!state.filePath) return

    console.log('Loading PDF content for:', state.filePath)

    // Use PDF.js for reliable, secure PDF viewing
    await loadPDFWithPDFJS()
  }

  const loadPDFWithPDFJS = async () => {
    if (!state.filePath) return

    try {
      setIsLoading(true)
      console.log('Loading PDF with PDF.js:', state.filePath)

      // Get file content as base64 for PDF.js
      if (window.electronAPI?.files?.getFileContent) {
        const result = await window.electronAPI.files.getFileContent(state.filePath)
        console.log('PDF getFileContent result:', result ? 'success' : 'failed', result?.length || 0)

        if (result && result.trim() !== '') {
          // Convert base64 to Uint8Array for PDF.js
          const binaryString = atob(result)
          const bytes = new Uint8Array(binaryString.length)
          for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i)
          }

          // Load PDF document with PDF.js
          const loadingTask = pdfjsLib.getDocument({ data: bytes })
          const pdf = await loadingTask.promise

          console.log('PDF.js loaded successfully:', pdf.numPages, 'pages')
          setPdfDocument(pdf)
          setTotalPages(pdf.numPages)
          setCurrentPage(1)

          // Small delay to ensure React state updates and canvas is ready
          setTimeout(async () => {
            console.log('Rendering first page after state update...')
            await renderPage(pdf, 1)
            setIsLoading(false) // Only stop loading after first page renders
          }, 200)
        } else {
          console.error('Empty or invalid file content received')
        }
      }
    } catch (error) {
      console.error('Failed to load PDF with PDF.js:', error)
      setIsLoading(false)
    }
    // Note: setIsLoading(false) is called after first page renders in setTimeout above
  }

  const renderPage = async (pdf: any, pageNumber: number) => {
    if (!pdf || !canvasRef.current) {
      console.log('Cannot render: missing pdf or canvas ref')
      return
    }

    try {
      console.log(`Starting to render page ${pageNumber}`)
      const page = await pdf.getPage(pageNumber)
      const canvas = canvasRef.current
      const context = canvas.getContext('2d')

      if (!context) {
        console.error('Failed to get canvas 2D context')
        return
      }

      // Wait for canvas to be properly mounted
      await new Promise(resolve => setTimeout(resolve, 100))

      // Calculate scale based on container width
      const containerWidth = canvas.parentElement?.clientWidth || 800
      const viewport = page.getViewport({ scale: 1 })
      const scale = Math.min((containerWidth * 0.9) / viewport.width, 2) // Max 2x scale
      const scaledViewport = page.getViewport({ scale })

      // Clear canvas first
      context.clearRect(0, 0, canvas.width, canvas.height)

      // Set canvas dimensions
      canvas.height = scaledViewport.height
      canvas.width = scaledViewport.width

      // Set canvas style for proper display
      canvas.style.maxWidth = '100%'
      canvas.style.height = 'auto'

      const renderContext = {
        canvasContext: context,
        viewport: scaledViewport
      }

      console.log(`Rendering page ${pageNumber} with scale ${scale}`)
      await page.render(renderContext).promise
      console.log(`PDF.js successfully rendered page ${pageNumber}`)
    } catch (error) {
      console.error('Failed to render PDF page:', error)
    }
  }

  const loadPDFAsDataURL = async () => {
    if (!state.filePath) return

    try {
      // Read file content as base64
      if (window.electronAPI?.files?.getFileContent) {
        const result = await window.electronAPI.files.getFileContent(state.filePath)
        console.log('PDF getFileContent result:', result ? 'success' : 'failed', result?.length || 0)
        if (result && result.trim() !== '') {
          // Create data URL for PDF - simple and reliable!
          const dataUrl = `data:application/pdf;base64,${result}`
          console.log('Setting PDF data URL:', dataUrl.substring(0, 50) + '...')
          setPdfUrl(dataUrl)
        } else {
          console.error('Empty or invalid file content received')
          setPdfUrl(null)
        }
      }
    } catch (error) {
      console.error('Failed to load PDF as data URL:', error)
      setPdfUrl(null)
    }
  }

  const handleClose = () => {
    pdfViewerService.closePDF()
    setPdfDocument(null)
    setCurrentPage(1)
    setTotalPages(0)
    onClose()
  }

  const handleNextPage = async () => {
    if (pdfDocument && currentPage < totalPages) {
      const nextPage = currentPage + 1
      setCurrentPage(nextPage)
      await renderPage(pdfDocument, nextPage)
    }
  }

  const handlePrevPage = async () => {
    if (pdfDocument && currentPage > 1) {
      const prevPage = currentPage - 1
      setCurrentPage(prevPage)
      await renderPage(pdfDocument, prevPage)
    }
  }

  const handleZoomIn = () => {
    pdfViewerService.zoomIn()
  }

  const handleZoomOut = () => {
    pdfViewerService.zoomOut()
  }

  const handleResetZoom = () => {
    pdfViewerService.resetZoom()
  }

  const handleDownload = async () => {
    if (!state.filePath) return

    try {
      if (window.electronAPI?.shell?.openPath) {
        await window.electronAPI.shell.openPath(state.filePath)
      }
    } catch (error) {
      console.error('Failed to open PDF:', error)
    }
  }

  const handleExtractText = async () => {
    if (!state.filePath) return

    try {
      if (window.electronAPI?.files?.processFile) {
        const result = await window.electronAPI.files.processFile(state.filePath, 'pdf')
        if (result.success && result.content?.text) {
          // Copy text to clipboard
          await navigator.clipboard.writeText(result.content.text)
          // You could show a toast notification here
          console.log('Text extracted and copied to clipboard')
        }
      }
    } catch (error) {
      console.error('Failed to extract text:', error)
    }
  }

  if (!state.isOpen) {
    return null
  }

  return (
    <div className="fixed inset-0 bg-gray-900 z-50 flex font-sans">
      {/* Main PDF Viewer */}
      <div className="flex-1 bg-gray-800 flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-tertiary/50 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <button 
              onClick={handleClose} 
              className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
            >
              <FontAwesomeIcon icon={ICONS.arrowLeft} className="text-gray-400 text-sm" />
            </button>
            <FontAwesomeIcon icon={ICONS.filePdf} className="text-secondary text-lg" />
            <div className="flex flex-col">
              <span className="text-supplement1 font-semibold text-lg">{state.fileName}</span>
              <div className="flex items-center gap-2 text-xs text-gray-400">
                <span>PDF Document</span>
                {state.extractedText && (
                  <span className="flex items-center gap-1">
                    <FontAwesomeIcon icon={ICONS.robot} className="text-xs" />
                    Text Extracted
                  </span>
                )}
              </div>
            </div>
          </div>
          
          {/* Controls */}
          <div className="flex items-center gap-2">
            <button 
              onClick={handleZoomOut}
              disabled={!pdfViewerService.canZoomOut()}
              className="p-2 hover:bg-gray-700 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <FontAwesomeIcon icon={ICONS.magnifyingGlassMinus} className="text-gray-400 text-sm" />
            </button>
            <button 
              onClick={handleResetZoom}
              className="px-3 py-1 hover:bg-gray-700 rounded transition-colors"
            >
              <span className="text-gray-400 text-sm">{state.zoom}%</span>
            </button>
            <button 
              onClick={handleZoomIn}
              disabled={!pdfViewerService.canZoomIn()}
              className="p-2 hover:bg-gray-700 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <FontAwesomeIcon icon={ICONS.magnifyingGlassPlus} className="text-gray-400 text-sm" />
            </button>
            <div className="w-px h-6 bg-gray-600 mx-2"></div>
            <button 
              onClick={handleExtractText}
              className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
              title="Extract text to clipboard"
            >
              <FontAwesomeIcon icon={ICONS.fileText} className="text-gray-400 text-sm" />
            </button>
            <button
              onClick={handleDownload}
              className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
              title="Open with system app"
            >
              <FontAwesomeIcon icon={ICONS.externalLink} className="text-gray-400 text-sm" />
            </button>
          </div>
        </div>

        {/* PDF Content */}
        <div className="flex-1 overflow-hidden bg-gray-900 p-4">
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center text-gray-400">
                <FontAwesomeIcon icon={ICONS.spinner} className="text-4xl mb-4 animate-spin" />
                <p className="text-lg font-medium">Loading PDF...</p>
                <p className="text-sm">Please wait while we load the document</p>
              </div>
            </div>
          ) : state.error ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center text-red-400">
                <FontAwesomeIcon icon={ICONS.exclamationTriangle} className="text-4xl mb-4" />
                <p className="text-lg font-medium">Failed to load PDF</p>
                <p className="text-sm">{state.error}</p>
                <button
                  onClick={() => pdfViewerService.openPDF(state.filePath!, state.fileName!)}
                  className="mt-4 px-4 py-2 bg-secondary hover:bg-secondary/80 text-gray-900 rounded-lg transition-colors"
                >
                  Try Again
                </button>
              </div>
            </div>
          ) : pdfDocument ? (
            <div className="h-full bg-gray-800 rounded-lg overflow-hidden flex flex-col">
              {/* PDF.js Canvas Viewer */}
              <div className="flex-1 overflow-auto p-4 flex justify-center">
                <canvas
                  ref={canvasRef}
                  className="max-w-full h-auto border border-gray-600 rounded shadow-lg"
                  style={{
                    transform: `scale(${state.zoom / 100})`,
                    transformOrigin: 'top center'
                  }}
                />
              </div>

              {/* Page Navigation */}
              <div className="flex items-center justify-center gap-4 p-4 bg-gray-700 border-t border-gray-600">
                <button
                  onClick={handlePrevPage}
                  disabled={currentPage <= 1}
                  className="px-3 py-2 bg-gray-600 hover:bg-gray-500 disabled:bg-gray-800 disabled:text-gray-500 text-white rounded transition-colors"
                >
                  <FontAwesomeIcon icon={ICONS.chevronLeft} />
                </button>

                <span className="text-white font-medium">
                  Page {currentPage} of {totalPages}
                </span>

                <button
                  onClick={handleNextPage}
                  disabled={currentPage >= totalPages}
                  className="px-3 py-2 bg-gray-600 hover:bg-gray-500 disabled:bg-gray-800 disabled:text-gray-500 text-white rounded transition-colors"
                >
                  <FontAwesomeIcon icon={ICONS.chevronRight} />
                </button>
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center text-gray-400">
                <FontAwesomeIcon icon={ICONS.filePdf} className="text-4xl mb-4" />
                <p className="text-lg font-medium">No PDF content available</p>
                <p className="text-sm">Unable to load the PDF document</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Right Sidebar - File Info & Actions */}
      <div className="w-80 bg-gray-800 border-l border-tertiary/50 flex flex-col">
        {/* File Info */}
        <div className="p-4 border-b border-tertiary/50">
          <div className="flex items-center gap-2 mb-3">
            <FontAwesomeIcon icon={ICONS.filePdf} className="text-secondary text-lg" />
            <span className="text-supplement1 font-semibold text-sm">{state.fileName}</span>
          </div>
          <div className="text-xs text-gray-400 space-y-1">
            <div>Type: PDF Document</div>
            <div>Path: {state.filePath}</div>
            {state.extractedText && (
              <div>Text: {state.extractedText.length} characters</div>
            )}
          </div>
        </div>

        {/* Actions */}
        <div className="p-4 space-y-3">
          <button 
            onClick={handleExtractText}
            className="w-full p-3 bg-primary hover:bg-primary/80 text-gray-900 rounded-lg transition-colors flex items-center justify-center gap-2"
          >
            <FontAwesomeIcon icon={ICONS.fileText} className="text-sm" />
            Extract Text
          </button>
          
          <button
            onClick={handleDownload}
            className="w-full p-3 bg-gray-700 hover:bg-gray-600 text-supplement1 rounded-lg transition-colors flex items-center justify-center gap-2"
          >
            <FontAwesomeIcon icon={ICONS.externalLink} className="text-sm" />
            Open with System App
          </button>
        </div>

        {/* Extracted Text Preview */}
        {state.extractedText && (
          <div className="flex-1 p-4 border-t border-tertiary/50">
            <h4 className="text-supplement1 font-medium text-sm mb-2">Extracted Text</h4>
            <div className="bg-gray-900 rounded-lg p-3 text-xs text-gray-300 max-h-64 overflow-y-auto">
              <pre className="whitespace-pre-wrap font-mono">
                {state.extractedText.substring(0, 1000)}
                {state.extractedText.length > 1000 && '...'}
              </pre>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default PDFViewerOverlay
