classDiagram
    class FilePageOverlay {
        +FileViewerState state
        +PDFViewerState pdfState
        +FileTypeInfo fileTypeInfo
        +string fileContent
        +detectFileType(fileName) FileTypeInfo
        +isMermaidContent(content) boolean
        +renderFileContent() JSX.Element
        +handleClose() void
    }
    
    class FileViewerService {
        -FileViewerState state
        -subscribers Array
        +openFile(path, name, type) void
        +closeFile() void
        +setZoom(zoom) void
        +getState() FileViewerState
    }
    
    class MermaidRenderer {
        +string content
        +renderMermaid() void
        +render() JSX.Element
    }
    
    FilePageOverlay --> FileViewerService : uses
    FilePageOverlay --> MermaidRenderer : contains
    FileViewerService --> FilePageOverlay : notifies
