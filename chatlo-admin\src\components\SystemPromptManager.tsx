import React, { useState, useEffect } from 'react'
import { chatLoAPI } from '../services/ChatLoAPI'
import { SystemPrompts } from '../types/api'

export const SystemPromptManager: React.FC = () => {
  const [prompts, setPrompts] = useState<SystemPrompts>({
    extraction: '',
    analysis: '',
    validation: '',
    fallback: ''
  })
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [lastSaved, setLastSaved] = useState<string | null>(null)

  useEffect(() => {
    loadSystemPrompts()
  }, [])

  const loadSystemPrompts = async () => {
    try {
      setIsLoading(true)
      // Mock data for now - replace with actual API call
      const mockPrompts: SystemPrompts = {
        extraction: 'You are an AI assistant specialized in extracting structured information from text...',
        analysis: 'You are an AI assistant that analyzes extracted data and provides insights...',
        validation: 'You are an AI assistant that validates the accuracy and completeness of extracted data...',
        fallback: 'You are a helpful AI assistant. Please provide a clear and accurate response...'
      }
      setPrompts(mockPrompts)
    } catch (error) {
      console.error('Failed to load system prompts:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const saveSystemPrompts = async () => {
    try {
      setIsSaving(true)
      await chatLoAPI.updateSystemPrompts(prompts)
      setLastSaved(new Date().toLocaleTimeString())
    } catch (error) {
      console.error('Failed to save system prompts:', error)
      alert('Failed to save system prompts')
    } finally {
      setIsSaving(false)
    }
  }

  const updatePrompt = (type: keyof SystemPrompts, value: string) => {
    setPrompts(prev => ({
      ...prev,
      [type]: value
    }))
  }

  if (isLoading) {
    return (
      <div className="h-full bg-gray-900 text-white p-6 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-500 border-t-transparent mx-auto mb-4"></div>
          <p className="text-gray-400">Loading system prompts...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full bg-gray-900 text-white p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">📝 System Prompt Manager</h1>
          <p className="text-gray-400">Configure system prompts for different AI processing stages</p>
        </div>

        {/* Save Controls */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-4 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={saveSystemPrompts}
                disabled={isSaving}
                className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center gap-2"
              >
                {isSaving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent" />
                    Saving...
                  </>
                ) : (
                  <>
                    <span>💾</span>
                    Save All Prompts
                  </>
                )}
              </button>
              <button
                onClick={loadSystemPrompts}
                className="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center gap-2"
              >
                <span>🔄</span>
                Reload
              </button>
            </div>
            {lastSaved && (
              <div className="text-sm text-gray-400">
                Last saved: {lastSaved}
              </div>
            )}
          </div>
        </div>

        {/* Prompt Editors */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Extraction Prompt */}
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-white mb-2 flex items-center gap-2">
                <span className="text-blue-400">🔍</span>
                Extraction Prompt
              </h3>
              <p className="text-sm text-gray-400">Used for extracting structured data from raw content</p>
            </div>
            <textarea
              value={prompts.extraction}
              onChange={(e) => updatePrompt('extraction', e.target.value)}
              className="w-full h-48 bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none font-mono text-sm"
              placeholder="Enter extraction prompt..."
            />
          </div>

          {/* Analysis Prompt */}
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-white mb-2 flex items-center gap-2">
                <span className="text-green-400">📊</span>
                Analysis Prompt
              </h3>
              <p className="text-sm text-gray-400">Used for analyzing extracted data and generating insights</p>
            </div>
            <textarea
              value={prompts.analysis}
              onChange={(e) => updatePrompt('analysis', e.target.value)}
              className="w-full h-48 bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none font-mono text-sm"
              placeholder="Enter analysis prompt..."
            />
          </div>

          {/* Validation Prompt */}
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-white mb-2 flex items-center gap-2">
                <span className="text-yellow-400">✅</span>
                Validation Prompt
              </h3>
              <p className="text-sm text-gray-400">Used for validating accuracy and completeness of results</p>
            </div>
            <textarea
              value={prompts.validation}
              onChange={(e) => updatePrompt('validation', e.target.value)}
              className="w-full h-48 bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent resize-none font-mono text-sm"
              placeholder="Enter validation prompt..."
            />
          </div>

          {/* Fallback Prompt */}
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-white mb-2 flex items-center gap-2">
                <span className="text-red-400">🛡️</span>
                Fallback Prompt
              </h3>
              <p className="text-sm text-gray-400">Used when other prompts fail or as a general-purpose prompt</p>
            </div>
            <textarea
              value={prompts.fallback}
              onChange={(e) => updatePrompt('fallback', e.target.value)}
              className="w-full h-48 bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent resize-none font-mono text-sm"
              placeholder="Enter fallback prompt..."
            />
          </div>
        </div>

        {/* Prompt Templates */}
        <div className="mt-8 bg-gray-800 rounded-lg border border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
            <span className="text-purple-400">📋</span>
            Prompt Templates
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <button className="bg-gray-700 hover:bg-gray-600 border border-gray-600 rounded-lg p-4 text-left transition-colors">
              <div className="font-medium text-white mb-1">Basic Extraction</div>
              <div className="text-sm text-gray-400">Simple data extraction template</div>
            </button>
            <button className="bg-gray-700 hover:bg-gray-600 border border-gray-600 rounded-lg p-4 text-left transition-colors">
              <div className="font-medium text-white mb-1">Research Analysis</div>
              <div className="text-sm text-gray-400">Academic research analysis template</div>
            </button>
            <button className="bg-gray-700 hover:bg-gray-600 border border-gray-600 rounded-lg p-4 text-left transition-colors">
              <div className="font-medium text-white mb-1">Business Intelligence</div>
              <div className="text-sm text-gray-400">Business data analysis template</div>
            </button>
            <button className="bg-gray-700 hover:bg-gray-600 border border-gray-600 rounded-lg p-4 text-left transition-colors">
              <div className="font-medium text-white mb-1">Technical Documentation</div>
              <div className="text-sm text-gray-400">Technical content processing template</div>
            </button>
          </div>
        </div>

        {/* Usage Statistics */}
        <div className="mt-8 bg-gray-800 rounded-lg border border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
            <span className="text-indigo-400">📈</span>
            Usage Statistics
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-gray-700 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-blue-400">1,247</div>
              <div className="text-sm text-gray-400">Extractions</div>
            </div>
            <div className="bg-gray-700 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-green-400">892</div>
              <div className="text-sm text-gray-400">Analyses</div>
            </div>
            <div className="bg-gray-700 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-yellow-400">156</div>
              <div className="text-sm text-gray-400">Validations</div>
            </div>
            <div className="bg-gray-700 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-red-400">23</div>
              <div className="text-sm text-gray-400">Fallbacks</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
