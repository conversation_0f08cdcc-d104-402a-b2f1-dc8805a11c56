import { useState, useEffect } from 'react'

export const useVerticalMode = () => {
  const [isVerticalMode, setIsVerticalMode] = useState(false)

  useEffect(() => {
    const checkVerticalMode = () => {
      // Consider vertical mode only for very narrow mobile screens
      const width = window.innerWidth
      const height = window.innerHeight
      const aspectRatio = width / height

      // Vertical mode: Only for very narrow mobile screens (width < 480px)
      // AND aspect ratio is very tall (< 0.6, which is more restrictive than 10:16 = 0.625)
      const isVertical = width < 480 && aspectRatio < 0.6
      setIsVerticalMode(isVertical)
    }

    // Check on mount
    checkVerticalMode()

    // Check on resize
    window.addEventListener('resize', checkVerticalMode)

    return () => {
      window.removeEventListener('resize', checkVerticalMode)
    }
  }, [])

  return isVerticalMode
}