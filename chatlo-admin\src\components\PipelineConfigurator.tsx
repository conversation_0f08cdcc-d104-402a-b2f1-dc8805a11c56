import React, { useState, useEffect } from 'react'
import { chatLoAPI } from '../services/ChatLoAPI'
import { pipelineAPI, Pipeline, ContextVault, DataSource, IntentMatch, PipelineResult, ProcessingRequest } from '../api/pipelineAPI'
import { PipelineConfig } from '../types/api'

export const PipelineConfigurator: React.FC = () => {
  const [selectedPipeline, setSelectedPipeline] = useState<Pipeline | null>(null)
  const [availablePipelines, setAvailablePipelines] = useState<Pipeline[]>([])
  const [selectedVault, setSelectedVault] = useState<ContextVault | null>(null)
  const [availableVaults, setAvailableVaults] = useState<ContextVault[]>([])
  const [dataSources, setDataSources] = useState<DataSource[]>([])
  const [smartInstruction, setSmartInstruction] = useState('')
  const [result, setResult] = useState<PipelineResult | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [isLoadingVaults, setIsLoadingVaults] = useState(true)
  const [isLoadingDataSources, setIsLoadingDataSources] = useState(false)
  const [currentModel, setCurrentModel] = useState('gemma3-8b-local')
  const [availableModels, setAvailableModels] = useState<string[]>([])

  // UI State
  const [isDataViewerCollapsed, setIsDataViewerCollapsed] = useState(false)
  const [isInstructionCollapsed, setIsInstructionCollapsed] = useState(false)
  const [isResultsCollapsed, setIsResultsCollapsed] = useState(false)

  // Load available pipelines and vaults on component mount
  useEffect(() => {
    loadAvailablePipelines()
    loadAvailableVaults()
    loadAvailableModels()
  }, [])

  // Load data sources when vault is selected
  useEffect(() => {
    if (selectedVault) {
      loadDataSources(selectedVault)
    } else {
      setDataSources([])
    }
  }, [selectedVault])

  const loadAvailablePipelines = async () => {
    try {
      const pipelines = await pipelineAPI.getAvailablePipelines()
      setAvailablePipelines(pipelines)
      if (pipelines.length > 0) {
        setSelectedPipeline(pipelines[0]) // Default to first pipeline
      }
    } catch (error) {
      console.error('Failed to load pipelines:', error)
    }
  }

  const loadAvailableModels = async () => {
    try {
      const models = await pipelineAPI.getAvailableModels()
      setAvailableModels(models)
      if (models.length > 0) {
        setCurrentModel(models[0]) // Default to first model
      }
    } catch (error) {
      console.error('Failed to load models:', error)
    }
  }

  const loadAvailableVaults = async () => {
    try {
      setIsLoadingVaults(true)
      const vaults = await pipelineAPI.getContextVaults()
      setAvailableVaults(vaults)
    } catch (error) {
      console.error('Failed to load vaults:', error)
    } finally {
      setIsLoadingVaults(false)
    }
  }

  const loadDataSources = async (vault: ContextVault) => {
    try {
      setIsLoadingDataSources(true)
      const sources = await pipelineAPI.getVaultDataSources(vault.id)
      setDataSources(sources.map(source => ({ ...source, expanded: false })))
    } catch (error) {
      console.error('Failed to load data sources:', error)
    } finally {
      setIsLoadingDataSources(false)
    }
  }

  const toggleDataSourceExpansion = (sourceId: string) => {
    setDataSources(prev => prev.map(source => 
      source.id === sourceId 
        ? { ...source, expanded: !source.expanded }
        : source
    ))
  }

  const processSmartInstruction = async () => {
    if (!smartInstruction.trim() || !selectedVault || !selectedPipeline) {
      setResult({
        success: false,
        message: 'Please select a pipeline, context vault and enter an instruction',
        timestamp: new Date().toISOString()
      })
      return
    }

    try {
      setIsProcessing(true)
      setResult(null)

      const processingRequest: ProcessingRequest = {
        pipelineId: selectedPipeline.id,
        vaultId: selectedVault.id,
        instruction: smartInstruction,
        modelName: currentModel
      }

      const result = await pipelineAPI.processInstruction(processingRequest)
      setResult(result)
    } catch (error) {
      setResult({
        success: false,
        message: `Processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date().toISOString()
      })
    } finally {
      setIsProcessing(false)
    }
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return `${bytes} B`
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`
  }

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getFileTypeIcon = (type: DataSource['type']): string => {
    switch (type) {
      case 'master.md': return '📋'
      case 'json': return '⚙️'
      case 'markdown': return '📝'
      case 'text': return '📄'
      default: return '📁'
    }
  }

  const getFileTypeColor = (type: DataSource['type']): string => {
    switch (type) {
      case 'master.md': return 'text-blue-400'
      case 'json': return 'text-yellow-400'
      case 'markdown': return 'text-green-400'
      case 'text': return 'text-gray-400'
      default: return 'text-purple-400'
    }
  }

  return (
    <div className="h-full bg-gray-900 text-white p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-4">
          <h1 className="text-2xl font-bold text-white mb-1">⚙️ Pipeline Configuration</h1>
          <p className="text-sm text-gray-400">Configure and test intelligent processing pipelines</p>
        </div>

        {/* Pipeline Configuration */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-4 space-y-4">
          {/* Pipeline Selector */}
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <label className="block text-xs font-medium text-gray-400 mb-1">Pipeline</label>
              <select
                value={selectedPipeline?.id || ''}
                onChange={(e) => {
                  const pipeline = availablePipelines.find(p => p.id === e.target.value)
                  setSelectedPipeline(pipeline || null)
                }}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-white focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                <option value="">Select Pipeline...</option>
                {availablePipelines.map(pipeline => (
                  <option key={pipeline.id} value={pipeline.id}>
                    {pipeline.name}: {pipeline.description}
                  </option>
                ))}
              </select>
            </div>
            <div className="flex-1">
              <label className="block text-xs font-medium text-gray-400 mb-1">Context Vault</label>
              <select
                value={selectedVault?.id || ''}
                onChange={(e) => {
                  const vault = availableVaults.find(v => v.id === e.target.value)
                  setSelectedVault(vault || null)
                }}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-white focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                <option value="">Select Vault...</option>
                {availableVaults.map(vault => (
                  <option key={vault.id} value={vault.id}>
                    {vault.icon === 'fa-user' ? '👤' : vault.icon === 'fa-briefcase' ? '💼' : '🔬'} {vault.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Collapsible Data Viewer */}
          <div className="border border-gray-600 rounded-lg">
            <button
              onClick={() => setIsDataViewerCollapsed(!isDataViewerCollapsed)}
              className="w-full flex items-center justify-between p-3 bg-gray-700 hover:bg-gray-600 transition-colors rounded-t-lg"
            >
              <span className="text-sm font-medium text-white">📊 Data Viewer</span>
              <span className="text-gray-400">{isDataViewerCollapsed ? '▶' : '▼'}</span>
            </button>
            {!isDataViewerCollapsed && (
              <div className="p-3 bg-gray-750 max-h-64 overflow-y-auto">
                {!selectedVault ? (
                  <div className="text-xs text-gray-400 text-center py-4">
                    Select a context vault to view data sources
                  </div>
                ) : isLoadingDataSources ? (
                  <div className="text-xs text-gray-400 text-center py-4">
                    Loading data sources...
                  </div>
                ) : dataSources.length === 0 ? (
                  <div className="text-xs text-gray-400 text-center py-4">
                    No data sources found in selected vault
                  </div>
                ) : (
                  <div className="space-y-2">
                    {dataSources.map((source) => (
                      <div key={source.id} className="border border-gray-600 rounded">
                        <button
                          onClick={() => toggleDataSourceExpansion(source.id)}
                          className="w-full flex items-center gap-2 p-2 text-left hover:bg-gray-600 transition-colors text-xs"
                        >
                          <span className="text-sm">{getFileTypeIcon(source.type)}</span>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <span className="font-medium text-white truncate">{source.name}</span>
                              <span className={`text-xs px-1 py-0.5 rounded ${getFileTypeColor(source.type)} bg-gray-800`}>
                                {source.type}
                              </span>
                            </div>
                            <div className="text-xs text-gray-400">
                              {formatFileSize(source.size)} • {formatDate(source.lastModified)}
                            </div>
                          </div>
                          <span className="text-gray-400 text-xs">
                            {source.expanded ? '▼' : '▶'}
                          </span>
                        </button>
                        {source.expanded && source.content && (
                          <div className="px-2 pb-2">
                            <div className="bg-gray-800 rounded p-2 text-xs">
                              <div className="text-gray-400 mb-1">Content Preview:</div>
                              <pre className="text-gray-300 whitespace-pre-wrap overflow-x-auto text-xs">
                                {source.content.length > 150
                                  ? source.content.substring(0, 150) + '...'
                                  : source.content
                                }
                              </pre>
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Collapsible Smart Instruction */}
          <div className="border border-gray-600 rounded-lg">
            <button
              onClick={() => setIsInstructionCollapsed(!isInstructionCollapsed)}
              className="w-full flex items-center justify-between p-3 bg-gray-700 hover:bg-gray-600 transition-colors rounded-t-lg"
            >
              <span className="text-sm font-medium text-white">🧠 Smart Instruction</span>
              <span className="text-gray-400">{isInstructionCollapsed ? '▶' : '▼'}</span>
            </button>
            {!isInstructionCollapsed && (
              <div className="p-3 bg-gray-750 space-y-3">
                <textarea
                  value={smartInstruction}
                  onChange={(e) => setSmartInstruction(e.target.value)}
                  placeholder="Enter your processing instruction..."
                  className="w-full h-20 bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500 resize-none"
                />
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <button
                      onClick={processSmartInstruction}
                      disabled={isProcessing || !selectedVault || !smartInstruction.trim() || !selectedPipeline}
                      className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-medium py-2 px-4 rounded text-sm transition-colors flex items-center gap-2"
                    >
                      {isProcessing ? (
                        <>
                          <div className="animate-spin rounded-full h-3 w-3 border-2 border-white border-t-transparent" />
                          Processing...
                        </>
                      ) : (
                        <>
                          <span>🚀</span>
                          Send
                        </>
                      )}
                    </button>
                    <div className="text-xs text-gray-400">
                      Current model:
                      <select
                        value={currentModel}
                        onChange={(e) => setCurrentModel(e.target.value)}
                        className="ml-1 bg-gray-700 border border-gray-600 rounded px-2 py-1 text-xs text-white focus:outline-none focus:ring-1 focus:ring-blue-500"
                      >
                        {availableModels.map(model => (
                          <option key={model} value={model}>{model}</option>
                        ))}
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Collapsible Results */}
          <div className="border border-gray-600 rounded-lg">
            <button
              onClick={() => setIsResultsCollapsed(!isResultsCollapsed)}
              className="w-full flex items-center justify-between p-3 bg-gray-700 hover:bg-gray-600 transition-colors rounded-t-lg"
            >
              <span className="text-sm font-medium text-white">📊 Results</span>
              <span className="text-gray-400">{isResultsCollapsed ? '▶' : '▼'}</span>
            </button>
            {!isResultsCollapsed && (
              <div className="p-3 bg-gray-750">
                {!result ? (
                  <div className="text-xs text-gray-400 text-center py-4">
                    Results will appear here after processing
                  </div>
                ) : (
                  <div className={`rounded p-3 text-xs ${
                    result.success
                      ? 'bg-green-900/30 border border-green-700'
                      : 'bg-red-900/30 border border-red-700'
                  }`}>
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-sm">
                        {result.success ? '✅' : '❌'}
                      </span>
                      <span className={`font-medium text-xs ${
                        result.success ? 'text-green-400' : 'text-red-400'
                      }`}>
                        {result.success ? 'Success' : 'Error'}
                      </span>
                      {result.processingTime && (
                        <span className="text-xs text-gray-400 ml-auto">
                          {result.processingTime}ms
                        </span>
                      )}
                      {result.completionScore && (
                        <span className="text-xs text-blue-400">
                          Score: {result.completionScore}%
                        </span>
                      )}
                    </div>

                    <div className="text-gray-300 mb-2 text-xs">
                      {result.message}
                    </div>

                    {result.success && result.data && (
                      <div className="space-y-2">
                        {result.data.analysis && (
                          <div>
                            <div className="text-xs font-medium text-gray-400 mb-1">Analysis:</div>
                            <div className="text-gray-300 text-xs">{result.data.analysis}</div>
                          </div>
                        )}

                        {result.data.insights && result.data.insights.length > 0 && (
                          <div>
                            <div className="text-xs font-medium text-gray-400 mb-1">Key Insights:</div>
                            <ul className="text-gray-300 space-y-1">
                              {result.data.insights.map((insight: string, index: number) => (
                                <li key={index} className="flex items-start gap-1 text-xs">
                                  <span className="text-blue-400 mt-0.5">•</span>
                                  <span>{insight}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}

                        {/* Intent Matching Metrics Table */}
                        {result.intentMatches && result.intentMatches.length > 0 && (
                          <div>
                            <div className="text-xs font-medium text-gray-400 mb-2">Metrics:</div>
                            <div className="bg-gray-800 rounded border border-gray-600 overflow-hidden">
                              <table className="w-full text-xs">
                                <thead>
                                  <tr className="bg-gray-700">
                                    <th className="text-left p-2 text-gray-300 font-medium">Intent</th>
                                    <th className="text-right p-2 text-gray-300 font-medium">Match %</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  {result.intentMatches.map((match, index) => (
                                    <tr key={index} className="border-t border-gray-600 hover:bg-gray-750">
                                      <td className="p-2 text-gray-300">{match.intent}</td>
                                      <td className="p-2 text-right">
                                        <span className={`font-medium ${
                                          match.matchPercentage >= 80 ? 'text-green-400' :
                                          match.matchPercentage >= 60 ? 'text-yellow-400' : 'text-red-400'
                                        }`}>
                                          {match.matchPercentage}%
                                        </span>
                                      </td>
                                    </tr>
                                  ))}
                                </tbody>
                              </table>
                            </div>
                          </div>
                        )}

                        {/* Master.md Output Preview */}
                        {result.masterMdOutput && (
                          <div>
                            <div className="text-xs font-medium text-gray-400 mb-1">Master.md Output:</div>
                            <div className="bg-gray-800 rounded p-2 max-h-32 overflow-y-auto">
                              <pre className="text-gray-300 whitespace-pre-wrap text-xs">
                                {result.masterMdOutput.length > 300
                                  ? result.masterMdOutput.substring(0, 300) + '...'
                                  : result.masterMdOutput
                                }
                              </pre>
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    <div className="text-xs text-gray-500 mt-2">
                      {formatDate(result.timestamp)}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
