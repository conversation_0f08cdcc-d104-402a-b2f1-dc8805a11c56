/**
 * Performance Monitor Service
 * Optimized for baseline hardware (9th gen i7 + RTX 2060)
 * Implements resource monitoring and automatic degradation
 */

import { BaseService } from './base'

interface PerformanceMetrics {
  cpuUsage: number
  memoryUsage: number
  processingTime: number
  timestamp: number
}

interface ProcessingBudget {
  immediate: number // < 10ms
  quick: number     // < 50ms
  batch: number     // Background processing
}

class PerformanceMonitor extends BaseService {
  private metrics: PerformanceMetrics[] = []
  private readonly MAX_METRICS_HISTORY = 100
  private readonly CPU_THRESHOLD = 50 // 50% CPU usage threshold

  // ADAPTIVE: Hardware-based thresholds (calculated on initialization)
  private JS_HEAP_THRESHOLD: number = 512 * 1024 * 1024 // Default 512MB, will be adjusted
  private SYSTEM_MEMORY_THRESHOLD: number = 0.85 // Default 85%, will be adjusted

  // System requirements tracking
  private systemProfile: {
    totalRAM: number
    cpuInfo: string
    meetsMinimumSpec: boolean
    performanceTier: 'minimum' | 'mid-range' | 'high-end'
  } | null = null

  // Legacy threshold for backward compatibility
  private readonly MEMORY_THRESHOLD = this.JS_HEAP_THRESHOLD

  private readonly PROCESSING_BUDGETS: ProcessingBudget = {
    immediate: 10,  // 10ms for immediate operations
    quick: 50,      // 50ms for quick operations
    batch: 200      // 200ms for batch operations
  }

  private isMonitoring = false
  private monitoringInterval: NodeJS.Timeout | null = null

  constructor() {
    super({
      name: 'PerformanceMonitor',
      autoInitialize: true
    })
  }

  /**
   * Initialize the service
   */
  protected async doInitialize(): Promise<void> {
    // Profile system hardware and set adaptive thresholds
    await this.profileSystemHardware()
    this.setAdaptiveThresholds()

    // Don't start monitoring automatically - only when needed
    this.logger.info('Performance monitor initialized with adaptive thresholds', 'doInitialize', {
      systemProfile: this.systemProfile,
      jsHeapThreshold: `${Math.round(this.JS_HEAP_THRESHOLD / 1024 / 1024)}MB`,
      systemMemoryThreshold: `${(this.SYSTEM_MEMORY_THRESHOLD * 100).toFixed(1)}%`,
      cpuThreshold: this.CPU_THRESHOLD,
      processingBudgets: this.PROCESSING_BUDGETS,
      note: 'Monitoring will start when first needed'
    })
  }

  /**
   * Health check implementation
   */
  protected async doHealthCheck(): Promise<boolean> {
    const isHealthy = this.isMonitoring && this.canProcess('immediate')
    this.logger.info('Health check completed', 'doHealthCheck', {
      isMonitoring: this.isMonitoring,
      canProcessImmediate: this.canProcess('immediate'),
      isHealthy
    })
    return isHealthy
  }

  /**
   * Cleanup implementation
   */
  protected async doCleanup(): Promise<void> {
    this.stopMonitoring()
    this.logger.info('Performance monitor cleaned up', 'doCleanup')
  }

  /**
   * Start performance monitoring
   */
  startMonitoring(): void {
    if (this.isMonitoring) {
      this.logger.debug('Performance monitoring already started', 'startMonitoring')
      return
    }

    this.isMonitoring = true
    this.monitoringInterval = setInterval(() => {
      this.collectMetrics()
    }, 5000) // Monitor every 5 seconds

    this.logger.info('Performance monitoring started', 'startMonitoring', {
      interval: '5000ms'
    })
  }

  /**
   * Stop performance monitoring
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) {
      this.logger.debug('Performance monitoring already stopped', 'stopMonitoring')
      return
    }

    this.isMonitoring = false
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval)
      this.monitoringInterval = null
    }

    this.logger.info('Performance monitoring stopped', 'stopMonitoring')
  }

  /**
   * Collect current performance metrics
   */
  private collectMetrics(): void {
    const jsHeapUsage = this.getMemoryUsage()
    const systemMemory = this.getSystemMemoryUsage()

    const metric: PerformanceMetrics = {
      cpuUsage: this.estimateCPUUsage(),
      memoryUsage: jsHeapUsage,
      processingTime: 0, // Will be updated by processing operations
      timestamp: Date.now()
    }

    this.metrics.push(metric)

    // Debug logging every 30 seconds
    if (this.metrics.length % 30 === 0) {
      console.log(`📊 [PERFORMANCE] JS Heap: ${Math.round(jsHeapUsage / 1024 / 1024)}MB, System RAM: ${systemMemory.percentage.toFixed(1)}% (${Math.round(systemMemory.used / 1024 / 1024 / 1024)}GB/${Math.round(systemMemory.total / 1024 / 1024 / 1024)}GB)`)
    }

    // Keep only recent metrics
    if (this.metrics.length > this.MAX_METRICS_HISTORY) {
      this.metrics.shift()
    }
  }

  /**
   * Estimate CPU usage (simplified approach for web environment)
   */
  private estimateCPUUsage(): number {
    // In a web environment, we can't directly measure CPU usage
    // We'll use processing time as a proxy
    const recentMetrics = this.metrics.slice(-5)
    if (recentMetrics.length === 0) return 0

    const avgProcessingTime = recentMetrics.reduce((sum, m) => sum + m.processingTime, 0) / recentMetrics.length
    
    // Convert processing time to estimated CPU usage percentage
    // This is a rough approximation
    return Math.min((avgProcessingTime / 100) * 100, 100)
  }

  /**
   * Get current memory usage
   */
  private getMemoryUsage(): number {
    if ('memory' in performance) {
      // @ts-ignore - performance.memory is available in Chrome
      return performance.memory.usedJSHeapSize || 0
    }
    return 0
  }

  private lastCanProcessLog = 0
  private readonly CAN_PROCESS_LOG_INTERVAL = 10000 // Log every 10 seconds max

  /**
   * Check if system can handle processing based on current load
   */
  canProcess(operationType: 'immediate' | 'quick' | 'batch'): boolean {
    const currentCPU = this.getCurrentCPUUsage()
    const currentJSHeap = this.getCurrentMemoryUsage()
    const systemMemory = this.getSystemMemoryUsage()

    // Check CPU threshold
    if (currentCPU > this.CPU_THRESHOLD) {
      console.warn(`CPU usage too high (${currentCPU}%) for ${operationType} processing`)
      return false
    }

    // Check JS heap threshold (more generous)
    if (currentJSHeap > this.JS_HEAP_THRESHOLD) {
      console.warn(`JS heap usage too high (${Math.round(currentJSHeap / 1024 / 1024)}MB) for ${operationType} processing`)
      return false
    }

    // Check system memory threshold (percentage-based)
    if (systemMemory.percentage > this.SYSTEM_MEMORY_THRESHOLD * 100) {
      console.warn(`System memory usage too high (${systemMemory.percentage.toFixed(1)}%) for ${operationType} processing`)
      return false
    }

    // Only log occasionally to prevent spam
    const now = Date.now()
    if (now - this.lastCanProcessLog > this.CAN_PROCESS_LOG_INTERVAL) {
      console.log(`✅ [PERFORMANCE] Can process ${operationType}: JS heap ${Math.round(currentJSHeap / 1024 / 1024)}MB, System ${systemMemory.percentage.toFixed(1)}%`)
      this.lastCanProcessLog = now
    }

    return true
  }

  /**
   * Get processing budget for operation type
   */
  getProcessingBudget(operationType: 'immediate' | 'quick' | 'batch'): number {
    return this.PROCESSING_BUDGETS[operationType]
  }

  /**
   * Record processing time for an operation
   */
  recordProcessingTime(operationType: 'immediate' | 'quick' | 'batch', processingTime: number): void {
    const budget = this.getProcessingBudget(operationType)
    const success = processingTime <= budget

    if (!success) {
      this.logger.warn('Operation exceeded processing budget', 'recordProcessingTime', {
        operationType,
        processingTime,
        budget,
        exceeded: processingTime - budget
      })
    } else {
      this.logger.debug('Processing time recorded', 'recordProcessingTime', {
        operationType,
        processingTime,
        budget,
        success
      })
    }

    // Update latest metric with processing time
    if (this.metrics.length > 0) {
      this.metrics[this.metrics.length - 1].processingTime = processingTime
    }

    // Track analytics for processing time
    this.trackProcessingAnalytics(operationType, processingTime, success)
  }

  /**
   * Track processing analytics (async import to avoid circular dependencies)
   */
  private async trackProcessingAnalytics(operationType: 'immediate' | 'quick' | 'batch', processingTime: number, success: boolean): Promise<void> {
    try {
      const { intelligenceAnalytics } = await import('./intelligenceAnalytics')
      intelligenceAnalytics.trackProcessingTime(operationType, processingTime, success)
    } catch (error) {
      // Silently fail to avoid breaking the performance monitor
      this.logger.debug('Analytics tracking failed', 'trackProcessingAnalytics', error)
    }
  }

  /**
   * Get current CPU usage estimate
   */
  getCurrentCPUUsage(): number {
    if (this.metrics.length === 0) return 0
    return this.metrics[this.metrics.length - 1].cpuUsage
  }

  /**
   * Get current memory usage (JS heap)
   */
  getCurrentMemoryUsage(): number {
    if (this.metrics.length === 0) return 0
    return this.metrics[this.metrics.length - 1].memoryUsage
  }

  /**
   * Get system memory usage (actual RAM)
   */
  getSystemMemoryUsage(): { used: number, total: number, percentage: number } {
    try {
      // Fallback to conservative estimates based on system profile
      const totalMem = this.systemProfile?.totalRAM || 16 * 1024 * 1024 * 1024 // 16GB default

      // Use browser memory API if available for estimation
      const memoryInfo = (performance as any).memory
      if (memoryInfo) {
        const jsHeapUsed = memoryInfo.usedJSHeapSize
        const jsHeapTotal = memoryInfo.totalJSHeapSize

        // Estimate system memory usage based on JS heap usage
        // This is a rough approximation
        const estimatedSystemUsage = Math.min(0.8, (jsHeapUsed / jsHeapTotal) * 0.3 + 0.3) // 30-80% range
        const usedMem = totalMem * estimatedSystemUsage

        return {
          used: usedMem,
          total: totalMem,
          percentage: estimatedSystemUsage * 100
        }
      }

      // Ultimate fallback: assume moderate usage
      const usedMem = totalMem * 0.4 // Assume 40% usage (conservative)
      return {
        used: usedMem,
        total: totalMem,
        percentage: 40
      }
    } catch (error) {
      console.warn('Could not get system memory info:', error)
      // Safe fallback
      const totalMem = 16 * 1024 * 1024 * 1024 // 16GB
      const usedMem = totalMem * 0.5 // Assume 50% usage
      return {
        used: usedMem,
        total: totalMem,
        percentage: 50
      }
    }
  }

  /**
   * Get performance summary
   */
  getPerformanceSummary(): {
    avgCPU: number
    avgMemory: number
    avgProcessingTime: number
    totalOperations: number
  } {
    if (this.metrics.length === 0) {
      return { avgCPU: 0, avgMemory: 0, avgProcessingTime: 0, totalOperations: 0 }
    }

    const avgCPU = this.metrics.reduce((sum, m) => sum + m.cpuUsage, 0) / this.metrics.length
    const avgMemory = this.metrics.reduce((sum, m) => sum + m.memoryUsage, 0) / this.metrics.length
    const avgProcessingTime = this.metrics.reduce((sum, m) => sum + m.processingTime, 0) / this.metrics.length

    return {
      avgCPU: Math.round(avgCPU),
      avgMemory: Math.round(avgMemory),
      avgProcessingTime: Math.round(avgProcessingTime),
      totalOperations: this.metrics.length
    }
  }

  /**
   * Check if system is under stress
   */
  isSystemUnderStress(): boolean {
    const currentCPU = this.getCurrentCPUUsage()
    const currentJSHeap = this.getCurrentMemoryUsage()
    const systemMemory = this.getSystemMemoryUsage()

    return currentCPU > this.CPU_THRESHOLD ||
           currentJSHeap > this.JS_HEAP_THRESHOLD ||
           systemMemory.percentage > this.SYSTEM_MEMORY_THRESHOLD * 100
  }

  /**
   * Profile system hardware to determine capabilities
   */
  private async profileSystemHardware(): Promise<void> {
    // Simple fallback: Assume mid-range system
    console.log(`🖥️ [SYSTEM] Using default hardware profile: 16GB RAM, mid-range tier`)
    this.systemProfile = {
      totalRAM: 16 * 1024 * 1024 * 1024, // Assume 16GB for better performance
      cpuInfo: 'Unknown',
      meetsMinimumSpec: true,
      performanceTier: 'mid-range'
    }

    // Set adaptive thresholds based on hardware
    this.setAdaptiveThresholds('mid-range', 16 * 1024 * 1024 * 1024)
  }



  /**
   * Set adaptive thresholds based on system profile
   */
  private setAdaptiveThresholds(performanceTier: string, totalRAM: number): void {
    switch (performanceTier) {
      case 'minimum': // 8GB RAM
        this.JS_HEAP_THRESHOLD = 256 * 1024 * 1024 // 256MB
        this.SYSTEM_MEMORY_THRESHOLD = 0.75 // 75%
        break

      case 'mid-range': // 16GB RAM
        this.JS_HEAP_THRESHOLD = 512 * 1024 * 1024 // 512MB
        this.SYSTEM_MEMORY_THRESHOLD = 0.80 // 80%
        break

      case 'high-end': // 32GB+ RAM
        this.JS_HEAP_THRESHOLD = 1024 * 1024 * 1024 // 1GB
        this.SYSTEM_MEMORY_THRESHOLD = 0.85 // 85%
        break
    }

    console.log(`⚙️ [ADAPTIVE] Set thresholds for ${performanceTier}: JS heap ${Math.round(this.JS_HEAP_THRESHOLD / 1024 / 1024)}MB, RAM ${(this.SYSTEM_MEMORY_THRESHOLD * 100).toFixed(1)}%`)
  }

  /**
   * Get system profile information
   */
  getSystemProfile(): typeof this.systemProfile {
    return this.systemProfile
  }

  /**
   * Get recommended processing mode based on current performance
   */
  getRecommendedProcessingMode(): 'full' | 'reduced' | 'minimal' {
    const currentCPU = this.getCurrentCPUUsage()
    const currentMemory = this.getCurrentMemoryUsage()

    if (currentCPU > 70 || currentMemory > this.MEMORY_THRESHOLD * 1.5) {
      return 'minimal'
    } else if (currentCPU > this.CPU_THRESHOLD || currentMemory > this.MEMORY_THRESHOLD) {
      return 'reduced'
    } else {
      return 'full'
    }
  }
}

// Export singleton instance
export const performanceMonitor = new PerformanceMonitor()
