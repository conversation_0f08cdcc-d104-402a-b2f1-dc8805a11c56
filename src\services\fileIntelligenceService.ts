import { BaseService, ServiceError, ServiceErrorCode } from './base'
import { localModelService } from './localModelService'
import { cacheManager } from './cacheManager'
import {
  FileIntelligenceData,
  EntitySelection,
  SmartAnnotation,
  FileInteraction
} from '../types/intelligenceTypes'
import {
  FileIntelligence,
  VaultIntelligence,
  KeyIdea,
  WeightedEntity,
  HumanConnection,
  FileIntelligenceConfig,
  ProcessingProgress,
  ProcessingResult,
  BatchProcessingStatus,
  IntentType,
  EntityType,
  PriorityLevel,
  DEFAULT_FILE_INTELLIGENCE_CONFIG,
  PRIORITY_WEIGHTS,
  ENTITY_PRIORITY_MAP
} from '../types/fileIntelligenceTypes'

import crypto from 'crypto'

/**
 * File Intelligence Service
 * Comprehensive service for intelligent document analysis using local models
 * Manages both legacy intelligence data and new file intelligence processing
 */
class FileIntelligenceService extends BaseService {
  private readonly INTELLIGENCE_FOLDER = '.intelligence'
  private readonly CONTEXT_FOLDER = '.context'
  private readonly FILES_FOLDER = 'files'
  private processingQueue: Map<string, ProcessingProgress> = new Map()
  private batchStatus: BatchProcessingStatus | null = null

  constructor() {
    super({
      name: 'FileIntelligenceService',
      autoInitialize: true
    })
  }

  protected async doInitialize(): Promise<void> {
    this.logger.info('File Intelligence Service initialized', 'doInitialize')
  }

  // ============================================================================
  // NEW FILE INTELLIGENCE PROCESSING METHODS
  // ============================================================================

  /**
   * Main entry point: Analyze a single file and generate intelligence
   */
  async analyzeFile(
    filePath: string,
    content: string,
    config: Partial<FileIntelligenceConfig> = {}
  ): Promise<ProcessingResult> {
    const fullConfig = { ...DEFAULT_FILE_INTELLIGENCE_CONFIG, ...config }
    const startTime = Date.now()

    return await this.executeOperationOrThrow(
      'analyzeFile',
      async () => {
        // Generate cache key based on file path and content hash
        const contentHash = crypto.createHash('sha256').update(content).digest('hex').substring(0, 16)
        const cacheKey = `file_intelligence_${this.generateDocumentHash(filePath)}_${contentHash}`

        // Check cache first
        const cachedResult = await cacheManager.get<ProcessingResult>(cacheKey)
        if (cachedResult) {
          console.log(`🔥 [INTELLIGENCE] Cache hit for file: ${filePath}`)
          this.updateProgress(filePath, 'complete', 100, 'Loaded from cache')
          return cachedResult
        }

        this.logger.info('Starting file analysis', 'analyzeFile', {
          filePath,
          contentLength: content.length,
          model: fullConfig.local_model_preferred,
          cacheKey
        })

        // Update progress
        this.updateProgress(filePath, 'extracting', 10, 'Extracting content structure')

        // 1. Extract basic file metadata and structure
        const fileMetadata = await this.extractFileMetadata(filePath, content)

        // Update progress
        this.updateProgress(filePath, 'analyzing', 30, 'Analyzing with local model')

        // 2. Use local model to extract key ideas (10+ minimum)
        const keyIdeas = await this.extractKeyIdeasWithLocalModel(content, fullConfig)

        // Update progress
        this.updateProgress(filePath, 'analyzing', 60, 'Extracting entities and connections')

        // 3. Extract weighted entities with human connection priority
        const weightedEntities = await this.extractWeightedEntities(content, keyIdeas)

        // 4. Extract human connections (highest priority)
        const humanConnections = await this.extractHumanConnections(content, weightedEntities)

        // Update progress
        this.updateProgress(filePath, 'storing', 90, 'Generating intelligence data')

        // 5. Build file intelligence structure
        const intelligence: FileIntelligence = {
          file_path: filePath,
          key_ideas: keyIdeas,
          weighted_entities: weightedEntities,
          human_connections: humanConnections,
          processing_confidence: this.calculateOverallConfidence(keyIdeas, weightedEntities),
          analysis_metadata: {
            processing_time_ms: Date.now() - startTime,
            model_used: fullConfig.local_model_preferred,
            timestamp: new Date().toISOString()
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }

        // 6. Store intelligence data
        await this.storeFileIntelligence(filePath, intelligence)

        // Update progress to complete
        this.updateProgress(filePath, 'complete', 100, 'Analysis complete')

        const processingTime = Date.now() - startTime

        const result: ProcessingResult = {
          success: true,
          file_path: filePath,
          processing_time_ms: processingTime,
          ideas_extracted: keyIdeas.length,
          entities_found: weightedEntities.length,
          human_connections: humanConnections.length,
          confidence_score: intelligence.processing_confidence,
          local_model_used: fullConfig.local_model_preferred
        }

        // Cache the result for future use
        await cacheManager.set(cacheKey, result)
        console.log(`💾 [INTELLIGENCE] Cached analysis result for: ${filePath}`)

        this.logger.info('File analysis completed', 'analyzeFile', result)
        return result
      },
      { filePath, contentLength: content.length }
    )
  }

  /**
   * Generate document hash for consistent file identification
   */
  private generateDocumentHash(filePath: string): string {
    return crypto.createHash('sha256').update(filePath).digest('hex').substring(0, 16)
  }

  /**
   * Extract key ideas using local model (minimum 10+ ideas)
   */
  private async extractKeyIdeasWithLocalModel(
    content: string,
    config: FileIntelligenceConfig
  ): Promise<KeyIdea[]> {
    try {
      // Check if local model is available
      const localModels = await localModelService.getAllLocalModels()
      const preferredModel = localModels.find(m => m.id === config.local_model_preferred)

      if (!preferredModel && !config.fallback_to_keyword_extraction) {
        throw new ServiceError(
          ServiceErrorCode.EXTERNAL_SERVICE_ERROR,
          `Preferred local model ${config.local_model_preferred} not available`,
          { serviceName: this.serviceName, operation: 'extractKeyIdeasWithLocalModel' }
        )
      }

      if (preferredModel) {
        // Use local model for analysis
        const prompt = this.buildKeyIdeaExtractionPrompt(content, config.min_ideas_required)
        const messages = [{ role: 'user', content: prompt }]

        const response = await localModelService.sendMessage(
          config.local_model_preferred,
          messages
        )

        return this.parseKeyIdeasFromResponse(response, config)
      } else {
        // Fallback to keyword-based extraction
        this.logger.warn('Falling back to keyword extraction', 'extractKeyIdeasWithLocalModel')
        return this.extractKeyIdeasKeywordBased(content, config)
      }
    } catch (error) {
      this.logger.error('Error in local model analysis', 'extractKeyIdeasWithLocalModel', error)

      if (config.fallback_to_keyword_extraction) {
        return this.extractKeyIdeasKeywordBased(content, config)
      }

      throw error
    }
  }

  /**
   * Build prompt for local model key idea extraction
   */
  private buildKeyIdeaExtractionPrompt(content: string, minIdeas: number): string {
    return `Analyze the following document and extract key ideas with relevance scores.

REQUIREMENTS:
- Extract at least ${minIdeas} key ideas
- Assign relevance score (0-100) for each idea
- Classify each idea by intent type: topic, knowledge, connection, action, reference
- Focus on actionable insights and important concepts
- Prioritize human connections (names, companies, roles)

DOCUMENT CONTENT:
${content.substring(0, 8000)} ${content.length > 8000 ? '...[truncated]' : ''}

RESPONSE FORMAT (JSON):
{
  "key_ideas": [
    {
      "text": "Key idea description",
      "relevance_score": 95,
      "intent_types": ["topic", "knowledge"],
      "context": "Where this idea was found"
    }
  ]
}

Extract comprehensive key ideas now:`
  }

  /**
   * Parse key ideas from local model response
   */
  private parseKeyIdeasFromResponse(response: string, config: FileIntelligenceConfig): KeyIdea[] {
    try {
      // Try to parse JSON response
      const jsonMatch = response.match(/\{[\s\S]*\}/)
      if (!jsonMatch) {
        throw new Error('No JSON found in response')
      }

      const parsed = JSON.parse(jsonMatch[0])
      const ideas: KeyIdea[] = []

      if (parsed.key_ideas && Array.isArray(parsed.key_ideas)) {
        parsed.key_ideas.forEach((idea: any, index: number) => {
          const keyIdea: KeyIdea = {
            id: `idea_${Date.now()}_${index}`,
            text: idea.text || '',
            relevance_score: Math.min(100, Math.max(0, idea.relevance_score || 50)),
            intent_types: Array.isArray(idea.intent_types) ? idea.intent_types : ['topic'],
            weight: this.calculateIdeaWeight(idea.relevance_score || 50),
            auto_selected: false, // Will be set later
            user_confirmed: false,
            context: idea.context || '',
            extracted_from: 'local_model'
          }
          ideas.push(keyIdea)
        })
      }

      // Sort by relevance score and auto-select top N
      ideas.sort((a, b) => b.relevance_score - a.relevance_score)
      ideas.slice(0, config.auto_select_top_n).forEach(idea => {
        idea.auto_selected = true
      })

      this.logger.info('Parsed key ideas from local model', 'parseKeyIdeasFromResponse', {
        totalIdeas: ideas.length,
        autoSelected: ideas.filter(i => i.auto_selected).length
      })

      return ideas
    } catch (error) {
      this.logger.error('Failed to parse local model response', 'parseKeyIdeasFromResponse', error)
      throw new ServiceError(
        ServiceErrorCode.PROCESSING_ERROR,
        'Failed to parse key ideas from local model response',
        { serviceName: this.serviceName, operation: 'parseKeyIdeasFromResponse', error }
      )
    }
  }

  /**
   * Get intelligence file path for a document
   */
  private getIntelligenceFilePath(vaultPath: string, documentHash: string): string {
    return `${vaultPath}/${this.INTELLIGENCE_FOLDER}/${documentHash}/intelligence.json`
  }

  /**
   * Fallback keyword-based key idea extraction
   */
  private extractKeyIdeasKeywordBased(content: string, config: FileIntelligenceConfig): KeyIdea[] {
    // Simple keyword-based extraction as fallback
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 20)
    const ideas: KeyIdea[] = []

    // Extract important sentences based on keywords
    const importantKeywords = [
      'requirement', 'specification', 'design', 'architecture', 'implementation',
      'feature', 'component', 'system', 'user', 'interface', 'data', 'security',
      'performance', 'testing', 'deployment', 'integration', 'api', 'database'
    ]

    sentences.forEach((sentence, index) => {
      const keywordCount = importantKeywords.filter(keyword =>
        sentence.toLowerCase().includes(keyword)
      ).length

      if (keywordCount > 0 && ideas.length < config.min_ideas_required * 2) {
        const relevanceScore = Math.min(90, 30 + (keywordCount * 15))

        ideas.push({
          id: `keyword_idea_${index}`,
          text: sentence.trim(),
          relevance_score: relevanceScore,
          intent_types: ['topic'],
          weight: this.calculateIdeaWeight(relevanceScore),
          auto_selected: ideas.length < config.auto_select_top_n,
          user_confirmed: false,
          context: 'keyword_extraction',
          extracted_from: 'keyword_fallback'
        })
      }
    })

    // Ensure minimum ideas requirement
    while (ideas.length < config.min_ideas_required && sentences.length > ideas.length) {
      const sentence = sentences[ideas.length]
      ideas.push({
        id: `fallback_idea_${ideas.length}`,
        text: sentence.trim(),
        relevance_score: 40,
        intent_types: ['topic'],
        weight: 0.4,
        auto_selected: false,
        user_confirmed: false,
        context: 'fallback_extraction',
        extracted_from: 'keyword_fallback'
      })
    }

    return ideas.slice(0, Math.max(config.min_ideas_required, 15))
  }

  /**
   * Calculate idea weight based on relevance score
   */
  private calculateIdeaWeight(relevanceScore: number): number {
    return Math.min(1.0, relevanceScore / 100)
  }

  /**
   * Extract file metadata
   */
  private async extractFileMetadata(filePath: string, content: string): Promise<any> {
    const stats = {
      path: filePath,
      type: filePath.split('.').pop()?.toLowerCase() || 'unknown',
      size_bytes: content.length,
      hash: this.generateDocumentHash(filePath),
      last_modified: new Date().toISOString()
    }
    return stats
  }

  /**
   * Analyze document structure
   */
  private analyzeDocumentStructure(content: string): any {
    const lines = content.split('\n')
    const words = content.split(/\s+/).filter(w => w.length > 0)

    return {
      word_count: words.length,
      line_count: lines.length,
      character_count: content.length,
      estimated_reading_time: Math.ceil(words.length / 200) // 200 words per minute
    }
  }

  /**
   * Extract weighted entities with priority classification
   */
  private async extractWeightedEntities(content: string, keyIdeas: KeyIdea[]): Promise<WeightedEntity[]> {
    const entities: WeightedEntity[] = []

    // Extract people names (high priority)
    const namePattern = /\b[A-Z][a-z]+ [A-Z][a-z]+\b/g
    const names = content.match(namePattern) || []
    names.forEach(name => {
      entities.push({
        text: name,
        type: 'person',
        confidence: 0.8,
        weight: PRIORITY_WEIGHTS.high,
        intent_types: ['connection'],
        context: 'Name pattern detection',
        priority_level: 'high'
      })
    })

    // Extract email addresses (high priority)
    const emailPattern = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g
    const emails = content.match(emailPattern) || []
    emails.forEach(email => {
      entities.push({
        text: email,
        type: 'email',
        confidence: 0.95,
        weight: PRIORITY_WEIGHTS.high,
        intent_types: ['connection'],
        context: 'Email pattern detection',
        priority_level: 'high'
      })
    })

    // Extract company names (high priority) - simple heuristic
    const companyPattern = /\b[A-Z][a-z]+ (Inc|Corp|LLC|Ltd|Company|Technologies|Systems|Solutions)\b/g
    const companies = content.match(companyPattern) || []
    companies.forEach(company => {
      entities.push({
        text: company,
        type: 'company',
        confidence: 0.7,
        weight: PRIORITY_WEIGHTS.high,
        intent_types: ['connection'],
        context: 'Company pattern detection',
        priority_level: 'high'
      })
    })

    return entities
  }

  /**
   * Extract human connections from entities
   */
  private async extractHumanConnections(content: string, entities: WeightedEntity[]): Promise<HumanConnection[]> {
    const connections: HumanConnection[] = []

    const people = entities.filter(e => e.type === 'person')
    const emails = entities.filter(e => e.type === 'email')
    const companies = entities.filter(e => e.type === 'company')

    people.forEach(person => {
      const connection: HumanConnection = {
        name: person.text,
        connection_strength: person.confidence,
        collaboration_context: person.context,
        document_mentions: 1,
        priority_weight: 1.0
      }

      // Try to find associated email
      const associatedEmail = emails.find(e =>
        content.indexOf(person.text) !== -1 && content.indexOf(e.text) !== -1
      )
      if (associatedEmail) {
        connection.email = associatedEmail.text
      }

      // Try to find associated company
      const associatedCompany = companies.find(c =>
        content.indexOf(person.text) !== -1 && content.indexOf(c.text) !== -1
      )
      if (associatedCompany) {
        connection.company = associatedCompany.text
      }

      connections.push(connection)
    })

    return connections
  }

  /**
   * Ensure intelligence directory exists
   */
  private async ensureIntelligenceDirectory(vaultPath: string, documentHash: string): Promise<void> {
    const intelligenceDir = `${vaultPath}/${this.INTELLIGENCE_FOLDER}/${documentHash}`

    if (window.electronAPI?.vault?.createDirectory) {
      const result = await window.electronAPI.vault.createDirectory(intelligenceDir)
      if (!result.success) {
        throw new ServiceError(
          ServiceErrorCode.STORAGE_ERROR,
          `Failed to create intelligence directory: ${result.error}`,
          { serviceName: this.serviceName, operation: 'ensureIntelligenceDirectory' }
        )
      }
    }
  }

  /**
   * Categorize entities by priority level
   */
  private categorizeEntitiesByPriority(entities: WeightedEntity[]): any {
    return {
      high_priority: entities.filter(e => e.priority_level === 'high'),
      medium_priority: entities.filter(e => e.priority_level === 'medium'),
      low_priority: entities.filter(e => e.priority_level === 'low')
    }
  }

  /**
   * Calculate overall confidence score
   */
  private calculateOverallConfidence(keyIdeas: KeyIdea[], entities: WeightedEntity[]): number {
    if (keyIdeas.length === 0) return 0

    const avgIdeaScore = keyIdeas.reduce((sum, idea) => sum + idea.relevance_score, 0) / keyIdeas.length
    const avgEntityConfidence = entities.length > 0
      ? entities.reduce((sum, entity) => sum + entity.confidence, 0) / entities.length
      : 0.5

    return Math.min(1.0, (avgIdeaScore / 100 + avgEntityConfidence) / 2)
  }

  /**
   * Extract file references from content
   */
  private extractFileReferences(content: string): string[] {
    const filePattern = /[\w\-_]+\.(pdf|doc|docx|txt|md|xlsx|ppt|pptx)/gi
    const matches = content.match(filePattern) || []
    return [...new Set(matches)] // Remove duplicates
  }

  /**
   * Store file intelligence data in context vault
   */
  private async storeFileIntelligence(filePath: string, intelligence: FileIntelligence): Promise<void> {
    // Implementation will store in .context/files/{hash}.json
    const hash = this.createFileHash(intelligence.file_path)
    this.logger.info('Storing file intelligence', 'storeFileIntelligence', { filePath, hash })
    // TODO: Implement actual storage to vault context folder
  }

  /**
   * Update processing progress
   */
  private updateProgress(
    filePath: string,
    stage: ProcessingProgress['stage'],
    percentage: number,
    operation: string
  ): void {
    const progress: ProcessingProgress = {
      file_path: filePath,
      stage,
      progress_percentage: percentage,
      current_operation: operation
    }

    this.processingQueue.set(filePath, progress)

    // Emit progress event for UI updates
    if (typeof window !== 'undefined' && window.electronAPI?.events?.emit) {
      window.electronAPI.events.emit('file-intelligence-progress', progress)
    }
  }

  /**
   * Get current processing progress for a file
   */
  getProcessingProgress(filePath: string): ProcessingProgress | null {
    return this.processingQueue.get(filePath) || null
  }

  /**
   * Clear processing progress for a file
   */
  clearProcessingProgress(filePath: string): void {
    this.processingQueue.delete(filePath)
  }

  // ============================================================================
  // LEGACY INTELLIGENCE DATA METHODS (Backward Compatibility)
  // ============================================================================

  /**
   * Save file intelligence data
   */
  async saveFileIntelligence(vaultPath: string, data: FileIntelligenceData): Promise<boolean> {
    const result = await this.executeOperation(
      'saveFileIntelligence',
      async () => {
        await this.ensureIntelligenceDirectory(vaultPath, data.document_hash)
        
        const filePath = this.getIntelligenceFilePath(vaultPath, data.document_hash)
        
        if (window.electronAPI?.vault?.writeFile) {
          const writeResult = await window.electronAPI.vault.writeFile(filePath, JSON.stringify(data, null, 2))
          if (!writeResult.success) {
            throw new ServiceError(
              ServiceErrorCode.STORAGE_ERROR,
              `Failed to save intelligence data: ${writeResult.error}`,
              { serviceName: this.serviceName, operation: 'saveFileIntelligence' }
            )
          }
        }
        
        return true
      }
    )

    if (!result.success) {
      this.logger.error('Failed to save file intelligence', 'saveFileIntelligence', result.error)
      // toastService.error('Save Failed', 'Could not save intelligence data')
      return false
    }

    return result.data!
  }

  /**
   * Load file intelligence data
   */
  async loadFileIntelligence(vaultPath: string, filePath: string): Promise<FileIntelligenceData | null> {
    const result = await this.executeOperation(
      'loadFileIntelligence',
      async () => {
        const documentHash = this.generateDocumentHash(filePath)
        const intelligenceFilePath = this.getIntelligenceFilePath(vaultPath, documentHash)
        
        if (window.electronAPI?.vault?.readFile) {
          const readResult = await window.electronAPI.vault.readFile(intelligenceFilePath)
          if (!readResult.success) {
            // File doesn't exist yet, return null
            return null
          }
          
          try {
            const data = JSON.parse(readResult.content!) as FileIntelligenceData
            return data
          } catch (error) {
            this.logger.warn('Invalid intelligence data format', 'loadFileIntelligence', error)
            return null
          }
        }
        
        return null
      }
    )

    if (!result.success) {
      this.logger.error('Failed to load file intelligence', 'loadFileIntelligence', result.error)
      return null
    }

    return result.data!
  }

  /**
   * Create initial file intelligence data structure
   */
  createInitialIntelligenceData(filePath: string, fileName: string, vaultName: string, contextId?: string): FileIntelligenceData {
    const documentHash = this.generateDocumentHash(filePath)
    
    return {
      document_hash: documentHash,
      file_path: filePath,
      file_name: fileName,
      file_type: fileName.split('.').pop()?.toLowerCase() || 'unknown',
      vault_name: vaultName,
      context_id: contextId,
      last_updated: new Date().toISOString(),
      entity_selections: [],
      smart_annotations: [],
      user_notes: [],
      ai_analysis: null,
      interaction_history: [],
      annotation_navigation: {
        current_note_index: 0,
        total_notes: 0,
        note_order: [],
        last_viewed_note: null,
        last_navigation_timestamp: new Date().toISOString(),
        created_timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Generate mock entities for demonstration (will be replaced with AI analysis)
   */
  generateMockEntities(fileName: string): EntitySelection[] {
    const fileExtension = fileName.split('.').pop()?.toLowerCase() || ''
    
    const mockEntities: Partial<EntitySelection>[] = []
    
    // Generate entities based on file type
    if (fileExtension === 'pdf') {
      mockEntities.push(
        { entity_text: 'UI Design', entity_type: 'content_category', confidence: 0.95 },
        { entity_text: 'Specifications', entity_type: 'technical_concept', confidence: 0.88 },
        { entity_text: 'Requirements', entity_type: 'requirement', confidence: 0.82 },
        { entity_text: 'Components', entity_type: 'feature', confidence: 0.75 },
        { entity_text: 'Design System', entity_type: 'methodology', confidence: 0.70 },
        { entity_text: 'User Interface', entity_type: 'technical_concept', confidence: 0.65 }
      )
    } else if (fileExtension === 'md') {
      mockEntities.push(
        { entity_text: 'Documentation', entity_type: 'content_category', confidence: 0.92 },
        { entity_text: 'API Reference', entity_type: 'technical_concept', confidence: 0.85 },
        { entity_text: 'Code Examples', entity_type: 'feature', confidence: 0.78 },
        { entity_text: 'Installation', entity_type: 'action_item', confidence: 0.72 },
        { entity_text: 'Configuration', entity_type: 'requirement', confidence: 0.68 }
      )
    } else {
      mockEntities.push(
        { entity_text: 'File Content', entity_type: 'content_category', confidence: 0.80 },
        { entity_text: 'Data Structure', entity_type: 'technical_concept', confidence: 0.75 },
        { entity_text: 'Information', entity_type: 'other', confidence: 0.70 }
      )
    }

    // Convert to full EntitySelection objects
    return mockEntities.map((entity, index) => ({
      entity_id: `entity_${Date.now()}_${index}`,
      entity_text: entity.entity_text!,
      entity_type: entity.entity_type!,
      confidence: entity.confidence!,
      is_selected: index < 3, // Pre-select top 3
      selection_timestamp: new Date().toISOString(),
      color_category: index === 0 ? 'primary' : index === 1 ? 'secondary' : index === 2 ? 'tertiary' : 'default',
      rank: index + 1,
      context_snippet: `Context for ${entity.entity_text}`
    }))
  }

  /**
   * Update entity selections
   */
  async updateEntitySelections(vaultPath: string, filePath: string, selections: EntitySelection[]): Promise<boolean> {
    const data = await this.loadFileIntelligence(vaultPath, filePath)
    if (!data) return false

    data.entity_selections = selections
    data.last_updated = new Date().toISOString()
    
    // Add interaction record
    const interaction: FileInteraction = {
      interaction_id: `int_${Date.now()}`,
      action_type: 'entity_select',
      timestamp: new Date().toISOString(),
      data: { selections: selections.map(s => ({ entity_id: s.entity_id, is_selected: s.is_selected })) },
      session_id: `session_${Date.now()}`
    }
    data.interaction_history.push(interaction)

    return await this.saveFileIntelligence(vaultPath, data)
  }

  /**
   * Add smart annotation
   */
  async addSmartAnnotation(vaultPath: string, filePath: string, annotation: SmartAnnotation): Promise<boolean> {
    const data = await this.loadFileIntelligence(vaultPath, filePath)
    if (!data) return false

    data.smart_annotations.push(annotation)
    data.last_updated = new Date().toISOString()
    
    // Add interaction record
    const interaction: FileInteraction = {
      interaction_id: `int_${Date.now()}`,
      action_type: 'annotation_create',
      timestamp: new Date().toISOString(),
      data: { annotation_id: annotation.annotation_id },
      session_id: `session_${Date.now()}`
    }
    data.interaction_history.push(interaction)

    return await this.saveFileIntelligence(vaultPath, data)
  }

  /**
   * Update smart annotation
   */
  async updateSmartAnnotation(vaultPath: string, filePath: string, annotationId: string, content: string): Promise<boolean> {
    const data = await this.loadFileIntelligence(vaultPath, filePath)
    if (!data) return false

    const annotation = data.smart_annotations.find(a => a.annotation_id === annotationId)
    if (!annotation) return false

    annotation.content = content
    annotation.last_edited = new Date().toISOString()
    data.last_updated = new Date().toISOString()
    
    // Add interaction record
    const interaction: FileInteraction = {
      interaction_id: `int_${Date.now()}`,
      action_type: 'annotation_edit',
      timestamp: new Date().toISOString(),
      data: { annotation_id: annotationId, new_content: content },
      session_id: `session_${Date.now()}`
    }
    data.interaction_history.push(interaction)

    return await this.saveFileIntelligence(vaultPath, data)
  }

  /**
   * Create a simple hash for file identification
   */
  private createFileHash(filePath: string): string {
    // Simple hash based on file path and timestamp
    const timestamp = Date.now().toString()
    const combined = filePath + timestamp
    let hash = 0
    for (let i = 0; i < combined.length; i++) {
      const char = combined.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(16)
  }
}

export const fileIntelligenceService = new FileIntelligenceService()
