import React, { useState, useMemo } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { ICONS } from './Icons/index'
import { useAppStore } from '../store'
import {
  modelCategories,
  enhanceModelInfo,
  searchModels,
  sortModels,
  getRecommendedModels,
  formatPricing,
  formatContextLength
} from '../utils/modelUtils'
import { OpenRouterModel } from '../types'

interface ModelSelectionOverlayProps {
  isOpen: boolean
  onClose: () => void
  onModelSelect: (modelId: string) => void
  selectedModel?: string
  title?: string
}

const ModelSelectionOverlay: React.FC<ModelSelectionOverlayProps> = ({
  isOpen,
  onClose,
  onModelSelect,
  selectedModel,
  title = 'Select Model'
}) => {
  const { models, isPrivateMode, localModels, localModelsAvailable } = useAppStore()
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('grid')

  // Get available models based on private mode
  const availableModels = useMemo(() => {
    // Transform local models to OpenRouterModel format
    const transformedLocalModels = localModels.map(localModel => ({
      id: localModel.id,
      name: localModel.name,
      description: `Local ${localModel.provider} model`,
      pricing: { prompt: "0", completion: "0" },
      context_length: 4096, // Default context length for local models
      architecture: { modality: "text", tokenizer: "unknown", instruct_type: undefined },
      top_provider: { max_completion_tokens: undefined },
      per_request_limits: undefined
    })) as OpenRouterModel[]

    if (isPrivateMode) {
      // In private mode, only show local models
      return transformedLocalModels
    }

    // In non-private mode, show both external and local models
    return [...models, ...transformedLocalModels]
  }, [isPrivateMode, localModels, models])

  // Filter and search models
  const filteredModels = useMemo(() => {
    let filtered = availableModels

    // Apply category filter
    const category = modelCategories.find(cat => cat.id === selectedCategory)
    if (category) {
      filtered = filtered.filter(category.filter)
    }

    // Apply search
    if (searchQuery.trim()) {
      filtered = searchModels(filtered, searchQuery)
    }

    // Sort models
    return sortModels(filtered, 'name')
  }, [availableModels, selectedCategory, searchQuery])

  // Get recommended models for quick access
  const recommendedModels = useMemo(() => {
    return getRecommendedModels(availableModels).slice(0, 4)
  }, [availableModels])

  const getCategoryIcon = (categoryId: string) => {
    switch (categoryId) {
      case 'free': return <FontAwesomeIcon icon={ICONS.gift} className="h-4 w-4" />
      case 'flagship': return <FontAwesomeIcon icon={ICONS.star} className="h-4 w-4" />
      case 'reasoning': return <FontAwesomeIcon icon={ICONS.brain} className="h-4 w-4" />
      case 'code': return <FontAwesomeIcon icon={ICONS.code} className="h-4 w-4" />
      case 'vision': return <FontAwesomeIcon icon={ICONS.eye} className="h-4 w-4" />
      case 'local': return <FontAwesomeIcon icon={ICONS.home} className="h-4 w-4" />
      default: return null
    }
  }

  const ModelCard: React.FC<{ model: OpenRouterModel; isSelected: boolean }> = ({ model, isSelected }) => {
    const enhanced = enhanceModelInfo(model)

    return (
      <div
        className={`
          p-4 rounded-lg border cursor-pointer transition-all hover:bg-neutral-800/50
          ${isSelected 
            ? 'border-indigo-500 bg-indigo-500/10' 
            : 'border-neutral-700 hover:border-neutral-600'
          }
        `}
        onClick={() => {
          onModelSelect(model.id)
          onClose()
        }}
      >
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1 min-w-0">
            <h4 className="font-medium text-sm truncate">{model.name}</h4>
            <p className="text-xs text-neutral-400">{enhanced.provider}</p>
          </div>
          <div className="flex items-center gap-1 ml-2">
            {enhanced.isFree && <div title="Free"><FontAwesomeIcon icon={ICONS.gift} className="h-3 w-3 text-green-400" /></div>}
            {enhanced.isFlagship && <div title="Flagship"><FontAwesomeIcon icon={ICONS.star} className="h-3 w-3 text-yellow-400" /></div>}
            {enhanced.isReasoning && <div title="Reasoning"><FontAwesomeIcon icon={ICONS.brain} className="h-3 w-3 text-purple-400" /></div>}
            {enhanced.isCode && <div title="Code"><FontAwesomeIcon icon={ICONS.code} className="h-3 w-3 text-blue-400" /></div>}
            {enhanced.isVision && <div title="Vision"><FontAwesomeIcon icon={ICONS.eye} className="h-3 w-3 text-indigo-400" /></div>}
            {isSelected && <FontAwesomeIcon icon={ICONS.check} className="h-3 w-3 text-indigo-400" />}
          </div>
        </div>

        <div className="flex items-center justify-between text-xs text-neutral-500 mb-2">
          <span>{formatContextLength(model.context_length)} context</span>
          <span>{formatPricing(model)}</span>
        </div>

        {model.description && (
          <p className="text-xs text-neutral-400 line-clamp-2">{model.description}</p>
        )}
      </div>
    )
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="relative w-full max-w-6xl h-[80vh] bg-neutral-900 border border-neutral-700 rounded-lg shadow-2xl flex overflow-hidden">
        {/* Left Section - Model Selection */}
        <div className="flex-1 flex flex-col">
          {/* Header */}
          <div className="p-6 border-b border-neutral-700">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-white">{title}</h2>
              <button
                onClick={onClose}
                className="p-2 hover:bg-neutral-800 rounded-lg transition-colors"
              >
                <FontAwesomeIcon icon={ICONS.times} className="text-neutral-400" />
              </button>
            </div>

            {/* Search */}
            <div className="relative mb-4">
              <FontAwesomeIcon 
                icon={ICONS.search} 
                className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neutral-400" 
              />
              <input
                type="text"
                placeholder="Search models..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-3 bg-neutral-800 border border-neutral-600 rounded-lg text-sm focus:outline-none focus:border-indigo-500"
              />
            </div>

            {/* View Options and Filters */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-sm text-neutral-400">View:</span>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded ${viewMode === 'list' ? 'bg-indigo-500 text-white' : 'bg-neutral-800 text-neutral-400'}`}
                >
                  <FontAwesomeIcon icon={ICONS.list} className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded ${viewMode === 'grid' ? 'bg-indigo-500 text-white' : 'bg-neutral-800 text-neutral-400'}`}
                >
                  <FontAwesomeIcon icon={ICONS.th} className="h-4 w-4" />
                </button>
              </div>

              {/* Category Filters */}
              <div className="flex flex-wrap gap-1">
                {modelCategories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`
                      px-3 py-1 rounded text-xs font-medium transition-colors flex items-center gap-1
                      ${selectedCategory === category.id
                        ? 'bg-indigo-500 text-white'
                        : 'bg-neutral-800 text-neutral-300 hover:bg-neutral-700'
                      }
                    `}
                  >
                    {getCategoryIcon(category.id)}
                    {category.name}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-6">
            {/* Recommended Models */}
            {!searchQuery && selectedCategory === 'all' && recommendedModels.length > 0 && (
              <div className="mb-6">
                <h3 className="text-sm font-medium mb-3 text-neutral-300">Recommended</h3>
                <div className={`grid gap-3 ${viewMode === 'grid' ? 'grid-cols-2' : 'grid-cols-1'}`}>
                  {recommendedModels.map((model) => (
                    <ModelCard
                      key={model.id}
                      model={model}
                      isSelected={model.id === selectedModel}
                    />
                  ))}
                </div>
              </div>
            )}

            {/* All Models */}
            <div>
              <h3 className="text-sm font-medium mb-3 text-neutral-300">
                All Models ({filteredModels.length})
              </h3>
              {filteredModels.length === 0 ? (
                <div className="text-center py-8 text-neutral-400">
                  <p>No models found</p>
                  {searchQuery && (
                    <button
                      onClick={() => setSearchQuery('')}
                      className="text-indigo-400 hover:text-indigo-300 text-sm mt-2"
                    >
                      Clear search
                    </button>
                  )}
                </div>
              ) : (
                <div className={`grid gap-3 ${viewMode === 'grid' ? 'grid-cols-2' : 'grid-cols-1'}`}>
                  {filteredModels.map((model) => (
                    <ModelCard
                      key={model.id}
                      model={model}
                      isSelected={model.id === selectedModel}
                    />
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Right Section - Quick Settings */}
        <div className="w-80 bg-neutral-800 border-l border-neutral-700 p-6">
          <h3 className="text-lg font-medium mb-4 text-white">Quick Settings</h3>
          
          {/* Private Mode Indicator */}
          {isPrivateMode && (
            <div className="mb-4 p-3 bg-secondary/20 border border-secondary/30 rounded-lg">
              <div className="flex items-center gap-2">
                <FontAwesomeIcon icon={ICONS.shield} className="text-secondary" />
                <span className="text-sm font-medium text-secondary">Private Mode</span>
              </div>
              <p className="text-xs text-gray-400 mt-1">
                {localModelsAvailable
                  ? `Showing ${localModels.length} local model${localModels.length !== 1 ? 's' : ''}`
                  : 'No local models available. Install Ollama or LM Studio to use private mode.'
                }
              </p>
            </div>
          )}

          {/* Selected Model Info */}
          {selectedModel && (
            <div className="mb-4 p-3 bg-indigo-900/20 border border-indigo-500/30 rounded-lg">
              <h4 className="text-sm font-medium text-indigo-300 mb-2">Selected Model</h4>
              <p className="text-sm text-white">{selectedModel}</p>
            </div>
          )}

          {/* Quick Actions */}
          <div className="space-y-3">
            <button
              onClick={() => {
                onModelSelect('')
                onClose()
              }}
              className="w-full p-3 bg-neutral-700 hover:bg-neutral-600 rounded-lg text-sm transition-colors"
            >
              Use Default Model
            </button>
            <button
              onClick={onClose}
              className="w-full p-3 bg-neutral-700 hover:bg-neutral-600 rounded-lg text-sm transition-colors"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ModelSelectionOverlay 