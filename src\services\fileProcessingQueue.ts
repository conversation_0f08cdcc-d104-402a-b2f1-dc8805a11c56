/**
 * File Processing Queue Service
 * Handles async file processing with error handling, retries, and performance monitoring
 */

import { BaseService } from './base'
import { performanceMonitor } from './performanceMonitor'
import { cacheManager } from './cacheManager'
import { streamingFileProcessor } from './streamingFileProcessor'

export interface FileProcessingTask {
  id: string
  filePath: string
  fileName: string
  fileSize: number
  vaultPath: string
  priority: 'high' | 'normal' | 'low'
  retryCount: number
  maxRetries: number
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'
  startTime?: number
  endTime?: number
  processingTime?: number
  error?: string
  result?: any
}

export interface ProcessingResult {
  taskId: string
  success: boolean
  processingTime: number
  error?: string
  result?: any
}

export interface QueueStats {
  totalTasks: number
  pendingTasks: number
  processingTasks: number
  completedTasks: number
  failedTasks: number
  cancelledTasks: number
  averageProcessingTime: number
  successRate: number
}

class FileProcessingQueue extends BaseService {
  private queue: FileProcessingTask[] = []
  private processing: Map<string, FileProcessingTask> = new Map()
  private completed: FileProcessingTask[] = []
  private failed: FileProcessingTask[] = []
  private isProcessing = false
  private maxConcurrentTasks = 3  // Conservative for stability
  private processingTimeouts: Map<string, NodeJS.Timeout> = new Map()
  private readonly DEFAULT_TIMEOUT = 30000 // 30 seconds - generous for slow machines
  private readonly MAX_HISTORY = 1000 // Keep last 1000 completed/failed tasks

  constructor() {
    super({
      name: 'FileProcessingQueue',
      autoInitialize: true
    })
  }

  protected async doInitialize(): Promise<void> {
    this.logger.info('File processing queue initialized', 'doInitialize')
  }

  protected async doCleanup(): Promise<void> {
    this.stopProcessing()
    this.clearTimeouts()
    this.logger.info('File processing queue cleaned up', 'doCleanup')
  }

  /**
   * Add a file to the processing queue
   */
  async addTask(
    filePath: string,
    fileName: string,
    fileSize: number,
    vaultPath: string,
    priority: 'high' | 'normal' | 'low' = 'normal'
  ): Promise<string> {
    return await this.executeOperationOrThrow(
      'addTask',
      async () => {
        const taskId = this.generateTaskId()
        const task: FileProcessingTask = {
          id: taskId,
          filePath,
          fileName,
          fileSize,
          vaultPath,
          priority,
          retryCount: 0,
          maxRetries: 3,
          status: 'pending'
        }

        // Insert based on priority
        this.insertByPriority(task)
        
        this.logger.info('Task added to queue', 'addTask', {
          taskId,
          fileName,
          priority,
          queueSize: this.queue.length
        })

        // Start processing if not already running
        if (!this.isProcessing) {
          this.startProcessing()
        }

        return taskId
      },
      { filePath, fileName, priority }
    )
  }

  /**
   * Start processing the queue
   */
  private startProcessing(): void {
    if (this.isProcessing) return

    this.isProcessing = true
    this.logger.info('Queue processing started', 'startProcessing')
    this.processNext()
  }

  /**
   * Stop processing the queue
   */
  stopProcessing(): void {
    this.isProcessing = false
    this.clearTimeouts()
    
    // Cancel all processing tasks
    for (const [taskId, task] of this.processing) {
      task.status = 'cancelled'
      this.processing.delete(taskId)
      this.completed.push(task)
    }

    this.logger.info('Queue processing stopped', 'stopProcessing')
  }

  private lastPerformanceCheck = 0
  private readonly PERFORMANCE_CHECK_INTERVAL = 5000 // Check performance every 5 seconds

  /**
   * Process next tasks in queue
   */
  private async processNext(): Promise<void> {
    if (!this.isProcessing) return

    // Check if we have capacity for more tasks
    const hasCapacity = this.processing.size < this.maxConcurrentTasks

    // Only check performance occasionally to reduce spam
    let canProcessPerformance = true
    const now = Date.now()
    if (now - this.lastPerformanceCheck > this.PERFORMANCE_CHECK_INTERVAL) {
      canProcessPerformance = performanceMonitor.canProcess('batch')
      this.lastPerformanceCheck = now
    }

    const canProcessMore = hasCapacity && canProcessPerformance

    if (!canProcessMore || this.queue.length === 0) {
      // Check again in 2 seconds (less frequent when idle)
      setTimeout(() => this.processNext(), 2000)
      return
    }

    // Get next task
    const task = this.queue.shift()
    if (!task) {
      setTimeout(() => this.processNext(), 1000)
      return
    }

    // Start processing
    this.processing.set(task.id, task)
    task.status = 'processing'
    task.startTime = Date.now()

    this.logger.info('Processing task started', 'processNext', {
      taskId: task.id,
      fileName: task.fileName,
      attempt: task.retryCount + 1
    })

    // Set timeout for this task
    const timeout = setTimeout(() => {
      this.handleTaskTimeout(task.id)
    }, this.DEFAULT_TIMEOUT)
    this.processingTimeouts.set(task.id, timeout)

    try {
      // Process the file
      const result = await this.processFile(task)
      await this.handleTaskCompletion(task.id, result)
    } catch (error) {
      await this.handleTaskError(task.id, error)
    }

    // Continue processing
    setTimeout(() => this.processNext(), 100)
  }

  /**
   * Process a single file
   */
  private async processFile(task: FileProcessingTask): Promise<any> {
    const startTime = Date.now()

    try {
      // Check cache first to avoid unnecessary processing
      const cacheKey = `file_processing_${task.id}_${task.fileName}`
      const cachedResult = await cacheManager.get(cacheKey)

      if (cachedResult) {
        console.log(`🔥 [QUEUE] Cache hit for file processing: ${task.fileName}`)
        return cachedResult
      }

      // Check if file should use streaming processing
      if (streamingFileProcessor.shouldUseStreaming(task.filePath)) {
        console.log(`🌊 [QUEUE] Using streaming processing for large file: ${task.fileName}`)

        const streamingResult = await streamingFileProcessor.processLargeFile(
          task.filePath,
          (progress) => {
            console.log(`🌊 [STREAMING] Progress: ${progress.currentPhase} - ${Math.round((progress.bytesProcessed / progress.totalBytes) * 100)}%`)
          }
        )

        if (streamingResult.success) {
          // Cache the streaming result
          await cacheManager.set(cacheKey, streamingResult)
          console.log(`💾 [QUEUE] Cached streaming result for: ${task.fileName}`)
          return streamingResult
        } else {
          throw new Error(`Streaming processing failed: ${streamingResult.error}`)
        }
      }

      // Import services dynamically to avoid circular dependencies
      const { fileAnalysisService } = await import('./fileAnalysisService')

      // Read file content using appropriate method based on file type
      console.log(`📖 [QUEUE-PROCESS] Reading file content: ${task.filePath}`)
      let fileContent = ''

      // Determine file type for proper processing
      const fileExtension = task.fileName.toLowerCase().split('.').pop() || ''
      const isPDF = fileExtension === 'pdf'
      const isImage = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(fileExtension)

      if (isPDF || isImage) {
        // Use file processor for PDFs and images (supports OCR)
        console.log(`🔧 [QUEUE-PROCESS] Using file processor for ${fileExtension.toUpperCase()} file`)
        if (window.electronAPI?.files?.processFile) {
          const processResult = await window.electronAPI.files.processFile(task.filePath, fileExtension)
          if (processResult.success && processResult.content?.text) {
            fileContent = processResult.content.text
            console.log(`✅ [QUEUE-PROCESS] File processed successfully: ${fileContent.length} characters`)
            console.log(`📊 [QUEUE-PROCESS] Processing metadata:`, processResult.content.metadata)
          } else {
            console.warn(`⚠️ [QUEUE-PROCESS] File processing failed, falling back to raw read`)
            // Fallback to raw read for text-based files
            if (window.electronAPI?.vault?.readFile) {
              const readResult = await window.electronAPI.vault.readFile(task.filePath)
              if (readResult.success) {
                fileContent = readResult.content || ''
              }
            }
          }
        } else {
          throw new Error('File processor API not available')
        }
      } else {
        // Use direct read for text-based files
        console.log(`📄 [QUEUE-PROCESS] Using direct read for ${fileExtension.toUpperCase()} file`)
        if (window.electronAPI?.vault?.readFile) {
          const readResult = await window.electronAPI.vault.readFile(task.filePath)
          if (readResult.success) {
            fileContent = readResult.content || ''
            console.log(`✅ [QUEUE-PROCESS] File content read successfully: ${fileContent.length} characters`)
          } else {
            throw new Error(`Failed to read file: ${readResult.error}`)
          }
        } else {
          throw new Error('Electron API not available for file reading')
        }
      }

      // Process the file with content
      console.log(`🧠 [QUEUE-PROCESS] Analyzing document: ${task.fileName}`)
      const result = await fileAnalysisService.analyzeAndStoreDocument(
        fileContent,
        task.filePath,
        task.vaultPath
      )
      console.log(`✅ [QUEUE-PROCESS] Document analysis complete for: ${task.fileName}`)

      const processingTime = Date.now() - startTime
      performanceMonitor.recordProcessingTime('batch', processingTime)

      // Cache the result for future use
      await cacheManager.set(cacheKey, result)
      console.log(`💾 [QUEUE] Cached processing result for: ${task.fileName}`)

      return result
    } catch (error) {
      const processingTime = Date.now() - startTime
      performanceMonitor.recordProcessingTime('batch', processingTime)
      throw error
    }
  }

  /**
   * Handle task completion
   */
  private async handleTaskCompletion(taskId: string, result: any): Promise<void> {
    const task = this.processing.get(taskId)
    if (!task) return

    // Clear timeout
    const timeout = this.processingTimeouts.get(taskId)
    if (timeout) {
      clearTimeout(timeout)
      this.processingTimeouts.delete(taskId)
    }

    // Update task
    task.status = 'completed'
    task.endTime = Date.now()
    task.processingTime = task.endTime - (task.startTime || task.endTime)
    task.result = result

    // Move to completed
    this.processing.delete(taskId)
    this.completed.push(task)

    // Trim history if needed
    if (this.completed.length > this.MAX_HISTORY) {
      this.completed.shift()
    }

    this.logger.info('Task completed successfully', 'handleTaskCompletion', {
      taskId,
      fileName: task.fileName,
      processingTime: task.processingTime
    })
  }

  /**
   * Handle task error
   */
  private async handleTaskError(taskId: string, error: any): Promise<void> {
    const task = this.processing.get(taskId)
    if (!task) return

    // Clear timeout
    const timeout = this.processingTimeouts.get(taskId)
    if (timeout) {
      clearTimeout(timeout)
      this.processingTimeouts.delete(taskId)
    }

    task.retryCount++
    task.error = error instanceof Error ? error.message : String(error)

    this.logger.warn('Task failed', 'handleTaskError', {
      taskId,
      fileName: task.fileName,
      attempt: task.retryCount,
      maxRetries: task.maxRetries,
      error: task.error
    })

    // Check if we should retry
    if (task.retryCount < task.maxRetries) {
      // Reset for retry
      task.status = 'pending'
      task.startTime = undefined
      task.endTime = undefined
      
      // Add back to queue with lower priority
      task.priority = 'low'
      this.processing.delete(taskId)
      this.insertByPriority(task)
      
      this.logger.info('Task queued for retry', 'handleTaskError', {
        taskId,
        fileName: task.fileName,
        retryCount: task.retryCount
      })
    } else {
      // Mark as failed
      task.status = 'failed'
      task.endTime = Date.now()
      task.processingTime = task.endTime - (task.startTime || task.endTime)

      this.processing.delete(taskId)
      this.failed.push(task)

      // Trim history if needed
      if (this.failed.length > this.MAX_HISTORY) {
        this.failed.shift()
      }

      this.logger.error('Task failed permanently', 'handleTaskError', {
        taskId,
        fileName: task.fileName,
        finalError: task.error
      })
    }
  }

  /**
   * Handle task timeout
   */
  private handleTaskTimeout(taskId: string): void {
    const task = this.processing.get(taskId)
    if (!task) return

    this.logger.warn('Task timed out', 'handleTaskTimeout', {
      taskId,
      fileName: task.fileName,
      timeout: this.DEFAULT_TIMEOUT
    })

    this.handleTaskError(taskId, new Error(`Task timed out after ${this.DEFAULT_TIMEOUT}ms`))
  }

  /**
   * Insert task by priority
   */
  private insertByPriority(task: FileProcessingTask): void {
    const priorityOrder = { high: 0, normal: 1, low: 2 }
    const taskPriority = priorityOrder[task.priority]

    let insertIndex = this.queue.length
    for (let i = 0; i < this.queue.length; i++) {
      const queuePriority = priorityOrder[this.queue[i].priority]

      // Same priority: prefer smaller files first (faster processing)
      if (taskPriority === queuePriority) {
        // Estimate file size from file extension for smart ordering
        const taskSize = this.estimateProcessingComplexity(task.fileName)
        const queueSize = this.estimateProcessingComplexity(this.queue[i].fileName)
        if (taskSize < queueSize) {
          insertIndex = i
          break
        }
      } else if (taskPriority < queuePriority) {
        insertIndex = i
        break
      }
    }

    this.queue.splice(insertIndex, 0, task)
  }

  /**
   * Estimate processing complexity based on file type
   * Lower values = faster processing (prioritized for slow machines)
   */
  private estimateProcessingComplexity(fileName: string): number {
    const ext = fileName.toLowerCase().split('.').pop() || ''

    // Fast files (text-based, small)
    if (['txt', 'md', 'json', 'csv'].includes(ext)) return 1

    // Medium files (structured documents)
    if (['docx', 'xlsx', 'pptx'].includes(ext)) return 2

    // Slow files (complex parsing, large)
    if (['pdf', 'zip', 'rar'].includes(ext)) return 3

    // Unknown files (assume medium complexity)
    return 2
  }

  /**
   * Generate unique task ID
   */
  private generateTaskId(): string {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Clear all timeouts
   */
  private clearTimeouts(): void {
    for (const timeout of this.processingTimeouts.values()) {
      clearTimeout(timeout)
    }
    this.processingTimeouts.clear()
  }

  /**
   * Get queue statistics
   */
  getStats(): QueueStats {
    const totalTasks = this.queue.length + this.processing.size + this.completed.length + this.failed.length
    const completedTasks = this.completed.length
    const failedTasks = this.failed.length
    
    const totalProcessingTime = this.completed.reduce((sum, task) => 
      sum + (task.processingTime || 0), 0
    )
    const averageProcessingTime = completedTasks > 0 ? totalProcessingTime / completedTasks : 0
    const successRate = totalTasks > 0 ? (completedTasks / (completedTasks + failedTasks)) * 100 : 0

    return {
      totalTasks,
      pendingTasks: this.queue.length,
      processingTasks: this.processing.size,
      completedTasks,
      failedTasks,
      cancelledTasks: this.completed.filter(t => t.status === 'cancelled').length,
      averageProcessingTime,
      successRate
    }
  }

  /**
   * Get all tasks (for monitoring)
   */
  getAllTasks(): {
    pending: FileProcessingTask[]
    processing: FileProcessingTask[]
    completed: FileProcessingTask[]
    failed: FileProcessingTask[]
  } {
    return {
      pending: [...this.queue],
      processing: Array.from(this.processing.values()),
      completed: [...this.completed],
      failed: [...this.failed]
    }
  }

  /**
   * Cancel a specific task
   */
  async cancelTask(taskId: string): Promise<boolean> {
    return await this.executeOperationOrThrow(
      'cancelTask',
      async () => {
        // Check if task is in queue
        const queueIndex = this.queue.findIndex(t => t.id === taskId)
        if (queueIndex !== -1) {
          const task = this.queue.splice(queueIndex, 1)[0]
          task.status = 'cancelled'
          this.completed.push(task)
          return true
        }

        // Check if task is processing
        const processingTask = this.processing.get(taskId)
        if (processingTask) {
          processingTask.status = 'cancelled'
          this.processing.delete(taskId)
          this.completed.push(processingTask)
          
          // Clear timeout
          const timeout = this.processingTimeouts.get(taskId)
          if (timeout) {
            clearTimeout(timeout)
            this.processingTimeouts.delete(taskId)
          }
          return true
        }

        return false
      },
      { taskId }
    )
  }

  /**
   * Clear completed and failed tasks
   */
  clearHistory(): void {
    this.completed.length = 0
    this.failed.length = 0
    this.logger.info('Task history cleared', 'clearHistory')
  }
}

export const fileProcessingQueue = new FileProcessingQueue()
