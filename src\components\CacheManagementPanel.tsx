/**
 * Cache Management Panel Component
 * Provides users with cache analytics, performance monitoring, and optimization controls
 */

import React, { useState, useEffect } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { 
  faChartLine, 
  faMemory, 
  faCog, 
  faTrash, 
  faRefresh,
  faExclamationTriangle,
  faCheckCircle,
  faInfoCircle,
  faBolt,
  faHdd
} from '@fortawesome/free-solid-svg-icons'

interface CacheStats {
  hitRate: number
  totalSize: string
  hotCacheSize: string
  warmCacheSize: string
  coldCacheSize: string
  systemTier: string
  recommendations: Array<{
    type: string
    priority: string
    title: string
    description: string
    action: string
  }>
}

interface CacheManagementPanelProps {
  isOpen: boolean
  onClose: () => void
}

const CacheManagementPanel: React.FC<CacheManagementPanelProps> = ({ isOpen, onClose }) => {
  const [stats, setStats] = useState<CacheStats | null>(null)
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState<'overview' | 'analytics' | 'settings'>('overview')

  useEffect(() => {
    if (isOpen) {
      loadCacheStats()
    }
  }, [isOpen])

  const loadCacheStats = async () => {
    setLoading(true)
    try {
      // This would call the cache analytics service
      const mockStats: CacheStats = {
        hitRate: 78.5,
        totalSize: '2.3 GB',
        hotCacheSize: '256 MB',
        warmCacheSize: '1.2 GB',
        coldCacheSize: '856 MB',
        systemTier: 'High-End',
        recommendations: [
          {
            type: 'performance',
            priority: 'medium',
            title: 'Increase Hot Cache Size',
            description: 'Your system can handle larger memory cache for better performance',
            action: 'Increase to 512MB'
          },
          {
            type: 'storage',
            priority: 'low',
            title: 'Archive Old Files',
            description: 'Some cached files haven\'t been accessed in 30+ days',
            action: 'Archive to cold storage'
          }
        ]
      }
      setStats(mockStats)
    } catch (error) {
      console.error('Failed to load cache stats:', error)
    } finally {
      setLoading(false)
    }
  }

  const clearCache = async (tier: 'hot' | 'warm' | 'cold' | 'all') => {
    setLoading(true)
    try {
      // This would call the cache manager
      console.log(`Clearing ${tier} cache...`)
      await new Promise(resolve => setTimeout(resolve, 1000)) // Mock delay
      await loadCacheStats()
    } catch (error) {
      console.error('Failed to clear cache:', error)
    } finally {
      setLoading(false)
    }
  }

  const optimizeCache = async () => {
    setLoading(true)
    try {
      // This would call the cache optimization service
      console.log('Optimizing cache configuration...')
      await new Promise(resolve => setTimeout(resolve, 2000)) // Mock delay
      await loadCacheStats()
    } catch (error) {
      console.error('Failed to optimize cache:', error)
    } finally {
      setLoading(false)
    }
  }

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high': return faExclamationTriangle
      case 'medium': return faInfoCircle
      case 'low': return faCheckCircle
      default: return faInfoCircle
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-400'
      case 'medium': return 'text-yellow-400'
      case 'low': return 'text-green-400'
      default: return 'text-gray-400'
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50 flex items-center justify-center">
      <div className="bg-neutral-900 rounded-xl border border-neutral-700 w-[90%] max-w-4xl h-[80%] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-neutral-700">
          <div className="flex items-center space-x-3">
            <FontAwesomeIcon icon={faMemory} className="text-indigo-400 text-xl" />
            <h2 className="text-xl font-semibold text-white">Cache Management</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            ✕
          </button>
        </div>

        {/* Tab Navigation */}
        <div className="flex border-b border-neutral-700">
          {[
            { id: 'overview', label: 'Overview', icon: faChartLine },
            { id: 'analytics', label: 'Analytics', icon: faChartLine },
            { id: 'settings', label: 'Settings', icon: faCog }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 px-6 py-3 transition-colors ${
                activeTab === tab.id
                  ? 'text-indigo-400 border-b-2 border-indigo-400'
                  : 'text-gray-400 hover:text-white'
              }`}
            >
              <FontAwesomeIcon icon={tab.icon} />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-6">
          {loading ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <FontAwesomeIcon icon={faRefresh} className="text-indigo-400 text-3xl animate-spin mb-4" />
                <p className="text-gray-400">Loading cache data...</p>
              </div>
            </div>
          ) : stats ? (
            <>
              {/* Overview Tab */}
              {activeTab === 'overview' && (
                <div className="space-y-6">
                  {/* Performance Summary */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="bg-neutral-800 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-gray-400">Hit Rate</span>
                        <FontAwesomeIcon icon={faBolt} className="text-green-400" />
                      </div>
                      <div className="text-2xl font-bold text-white">{stats.hitRate}%</div>
                      <div className="text-sm text-gray-500">Excellent performance</div>
                    </div>

                    <div className="bg-neutral-800 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-gray-400">Total Cache Size</span>
                        <FontAwesomeIcon icon={faHdd} className="text-blue-400" />
                      </div>
                      <div className="text-2xl font-bold text-white">{stats.totalSize}</div>
                      <div className="text-sm text-gray-500">Across all tiers</div>
                    </div>

                    <div className="bg-neutral-800 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-gray-400">System Tier</span>
                        <FontAwesomeIcon icon={faMemory} className="text-purple-400" />
                      </div>
                      <div className="text-2xl font-bold text-white">{stats.systemTier}</div>
                      <div className="text-sm text-gray-500">Optimized for your hardware</div>
                    </div>
                  </div>

                  {/* Cache Breakdown */}
                  <div className="bg-neutral-800 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-white mb-4">Cache Distribution</h3>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                          <span className="text-gray-300">Hot Cache (Memory)</span>
                        </div>
                        <span className="text-white font-medium">{stats.hotCacheSize}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                          <span className="text-gray-300">Warm Cache (SSD)</span>
                        </div>
                        <span className="text-white font-medium">{stats.warmCacheSize}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="w-3 h-3 bg-blue-400 rounded-full"></div>
                          <span className="text-gray-300">Cold Cache (Archive)</span>
                        </div>
                        <span className="text-white font-medium">{stats.coldCacheSize}</span>
                      </div>
                    </div>
                  </div>

                  {/* Recommendations */}
                  <div className="bg-neutral-800 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-white mb-4">Optimization Recommendations</h3>
                    <div className="space-y-3">
                      {stats.recommendations.map((rec, index) => (
                        <div key={index} className="flex items-start space-x-3 p-3 bg-neutral-700 rounded-lg">
                          <FontAwesomeIcon 
                            icon={getPriorityIcon(rec.priority)} 
                            className={`mt-1 ${getPriorityColor(rec.priority)}`}
                          />
                          <div className="flex-1">
                            <h4 className="font-medium text-white">{rec.title}</h4>
                            <p className="text-sm text-gray-400 mt-1">{rec.description}</p>
                            <p className="text-sm text-indigo-400 mt-2">{rec.action}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex space-x-4">
                    <button
                      onClick={optimizeCache}
                      className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors"
                    >
                      <FontAwesomeIcon icon={faBolt} />
                      <span>Optimize Cache</span>
                    </button>
                    <button
                      onClick={() => clearCache('all')}
                      className="flex items-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
                    >
                      <FontAwesomeIcon icon={faTrash} />
                      <span>Clear All Cache</span>
                    </button>
                    <button
                      onClick={loadCacheStats}
                      className="flex items-center space-x-2 px-4 py-2 bg-neutral-700 hover:bg-neutral-600 text-white rounded-lg transition-colors"
                    >
                      <FontAwesomeIcon icon={faRefresh} />
                      <span>Refresh</span>
                    </button>
                  </div>
                </div>
              )}

              {/* Analytics Tab */}
              {activeTab === 'analytics' && (
                <div className="text-center text-gray-400 py-12">
                  <FontAwesomeIcon icon={faChartLine} className="text-4xl mb-4" />
                  <p>Cache analytics charts and trends would be displayed here</p>
                  <p className="text-sm mt-2">Performance graphs, hit rate trends, and usage patterns</p>
                </div>
              )}

              {/* Settings Tab */}
              {activeTab === 'settings' && (
                <div className="text-center text-gray-400 py-12">
                  <FontAwesomeIcon icon={faCog} className="text-4xl mb-4" />
                  <p>Cache configuration settings would be displayed here</p>
                  <p className="text-sm mt-2">Cache size limits, eviction policies, and optimization preferences</p>
                </div>
              )}
            </>
          ) : (
            <div className="text-center text-gray-400 py-12">
              <p>Failed to load cache data</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default CacheManagementPanel
