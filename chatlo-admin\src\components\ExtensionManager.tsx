import React, { useState, useEffect } from 'react'

interface Extension {
  id: string
  name: string
  version: string
  description: string
  author: string
  category: 'core' | 'testing' | 'deployment' | 'analytics' | 'community'
  enabled: boolean
  installed: boolean
  icon: string
  badges: string[]
  downloadCount?: number
  rating?: number
  lastUpdated: string
  size: string
}

export const ExtensionManager: React.FC = () => {
  const [extensions, setExtensions] = useState<Extension[]>([])
  const [installedExtensions, setInstalledExtensions] = useState<Extension[]>([])
  const [availableExtensions, setAvailableExtensions] = useState<Extension[]>([])
  const [activeTab, setActiveTab] = useState<'installed' | 'available'>('installed')
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadExtensions()
  }, [])

  const loadExtensions = async () => {
    try {
      setIsLoading(true)
      
      // Mock installed extensions
      const mockInstalled: Extension[] = [
        {
          id: 'intelligence-testing',
          name: 'Intelligence Testing',
          version: '1.0.0',
          description: 'Test intelligence extraction with real LLM models',
          author: 'ChatLo Team',
          category: 'core',
          enabled: true,
          installed: true,
          icon: '🧪',
          badges: ['Core', 'Testing'],
          lastUpdated: '2025-08-02',
          size: '2.1 MB'
        },
        {
          id: 'system-prompts',
          name: 'System Prompts',
          version: '1.0.0',
          description: 'Manage system prompts and templates',
          author: 'ChatLo Team',
          category: 'core',
          enabled: true,
          installed: true,
          icon: '📝',
          badges: ['Core', 'Configuration'],
          lastUpdated: '2025-08-01',
          size: '1.8 MB'
        },
        {
          id: 'pipeline-config',
          name: 'Pipeline Config',
          version: '1.0.0',
          description: 'Configure LangChain processing pipelines',
          author: 'ChatLo Team',
          category: 'core',
          enabled: true,
          installed: true,
          icon: '⚙️',
          badges: ['Core', 'Pipeline'],
          lastUpdated: '2025-08-02',
          size: '3.2 MB'
        }
      ]

      // Mock available extensions
      const mockAvailable: Extension[] = [
        {
          id: 'advanced-analytics',
          name: 'Advanced Analytics',
          version: '2.1.0',
          description: 'Advanced analytics and reporting for ChatLo usage patterns',
          author: 'Analytics Team',
          category: 'analytics',
          enabled: false,
          installed: false,
          icon: '📈',
          badges: ['Analytics', 'Reporting'],
          downloadCount: 1247,
          rating: 4.8,
          lastUpdated: '2025-07-30',
          size: '4.5 MB'
        },
        {
          id: 'model-benchmarking',
          name: 'Model Benchmarking',
          version: '1.3.2',
          description: 'Comprehensive benchmarking suite for LLM models',
          author: 'Performance Team',
          category: 'testing',
          enabled: false,
          installed: false,
          icon: '🏃',
          badges: ['Testing', 'Performance'],
          downloadCount: 892,
          rating: 4.6,
          lastUpdated: '2025-07-28',
          size: '6.1 MB'
        },
        {
          id: 'custom-themes',
          name: 'Custom Themes',
          version: '1.0.5',
          description: 'Customize the admin dashboard appearance with themes',
          author: 'UI Team',
          category: 'community',
          enabled: false,
          installed: false,
          icon: '🎨',
          badges: ['UI', 'Themes'],
          downloadCount: 2156,
          rating: 4.9,
          lastUpdated: '2025-07-25',
          size: '1.2 MB'
        },
        {
          id: 'api-monitor',
          name: 'API Monitor',
          version: '2.0.1',
          description: 'Monitor API usage, rate limits, and performance metrics',
          author: 'DevOps Team',
          category: 'analytics',
          enabled: false,
          installed: false,
          icon: '📡',
          badges: ['API', 'Monitoring'],
          downloadCount: 756,
          rating: 4.4,
          lastUpdated: '2025-07-20',
          size: '3.8 MB'
        }
      ]

      setInstalledExtensions(mockInstalled)
      setAvailableExtensions(mockAvailable)
      setExtensions([...mockInstalled, ...mockAvailable])
    } catch (error) {
      console.error('Failed to load extensions:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const toggleExtension = (extensionId: string) => {
    setInstalledExtensions(prev => prev.map(ext => 
      ext.id === extensionId 
        ? { ...ext, enabled: !ext.enabled }
        : ext
    ))
  }

  const installExtension = async (extensionId: string) => {
    const extension = availableExtensions.find(ext => ext.id === extensionId)
    if (!extension) return

    // Mock installation process
    const installedExtension = { ...extension, installed: true, enabled: true }
    setInstalledExtensions(prev => [...prev, installedExtension])
    setAvailableExtensions(prev => prev.filter(ext => ext.id !== extensionId))
  }

  const uninstallExtension = async (extensionId: string) => {
    const extension = installedExtensions.find(ext => ext.id === extensionId)
    if (!extension || extension.category === 'core') return

    setInstalledExtensions(prev => prev.filter(ext => ext.id !== extensionId))
    setAvailableExtensions(prev => [...prev, { ...extension, installed: false, enabled: false }])
  }

  const filteredExtensions = (activeTab === 'installed' ? installedExtensions : availableExtensions)
    .filter(ext => {
      const matchesSearch = ext.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           ext.description.toLowerCase().includes(searchQuery.toLowerCase())
      const matchesCategory = selectedCategory === 'all' || ext.category === selectedCategory
      return matchesSearch && matchesCategory
    })

  const getCategoryColor = (category: string): string => {
    switch (category) {
      case 'core': return 'text-blue-400 bg-blue-900/30'
      case 'testing': return 'text-green-400 bg-green-900/30'
      case 'deployment': return 'text-red-400 bg-red-900/30'
      case 'analytics': return 'text-purple-400 bg-purple-900/30'
      case 'community': return 'text-yellow-400 bg-yellow-900/30'
      default: return 'text-gray-400 bg-gray-900/30'
    }
  }

  const renderStars = (rating: number): string => {
    return '★'.repeat(Math.floor(rating)) + '☆'.repeat(5 - Math.floor(rating))
  }

  if (isLoading) {
    return (
      <div className="h-full bg-gray-900 text-white p-6 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-500 border-t-transparent mx-auto mb-4"></div>
          <p className="text-gray-400">Loading extensions...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full bg-gray-900 text-white p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">🔌 Extension Manager</h1>
          <p className="text-gray-400">Manage and discover extensions for ChatLo Admin Dashboard</p>
        </div>

        {/* Controls */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6 mb-6">
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
            {/* Tabs */}
            <div className="flex bg-gray-700 rounded-lg p-1">
              <button
                onClick={() => setActiveTab('installed')}
                className={`px-4 py-2 rounded-md transition-colors ${
                  activeTab === 'installed'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-300 hover:text-white'
                }`}
              >
                Installed ({installedExtensions.length})
              </button>
              <button
                onClick={() => setActiveTab('available')}
                className={`px-4 py-2 rounded-md transition-colors ${
                  activeTab === 'available'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-300 hover:text-white'
                }`}
              >
                Available ({availableExtensions.length})
              </button>
            </div>

            {/* Search and Filter */}
            <div className="flex gap-4">
              <input
                type="text"
                placeholder="Search extensions..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white"
              >
                <option value="all">All Categories</option>
                <option value="core">Core</option>
                <option value="testing">Testing</option>
                <option value="deployment">Deployment</option>
                <option value="analytics">Analytics</option>
                <option value="community">Community</option>
              </select>
            </div>
          </div>
        </div>

        {/* Extensions Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredExtensions.map((extension) => (
            <div key={extension.id} className="bg-gray-800 rounded-lg border border-gray-700 p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  <span className="text-2xl">{extension.icon}</span>
                  <div>
                    <h3 className="font-semibold text-white">{extension.name}</h3>
                    <p className="text-sm text-gray-400">v{extension.version}</p>
                  </div>
                </div>
                {extension.installed && extension.category !== 'core' && (
                  <button
                    onClick={() => toggleExtension(extension.id)}
                    className={`w-12 h-6 rounded-full transition-colors ${
                      extension.enabled ? 'bg-green-600' : 'bg-gray-600'
                    }`}
                  >
                    <div className={`w-5 h-5 bg-white rounded-full transition-transform ${
                      extension.enabled ? 'translate-x-6' : 'translate-x-0.5'
                    }`} />
                  </button>
                )}
              </div>

              <p className="text-gray-300 text-sm mb-4">{extension.description}</p>

              <div className="flex flex-wrap gap-2 mb-4">
                <span className={`text-xs px-2 py-1 rounded ${getCategoryColor(extension.category)}`}>
                  {extension.category}
                </span>
                {extension.badges.map((badge) => (
                  <span key={badge} className="text-xs px-2 py-1 rounded bg-gray-700 text-gray-300">
                    {badge}
                  </span>
                ))}
              </div>

              <div className="text-sm text-gray-400 mb-4">
                <div className="flex justify-between">
                  <span>Author:</span>
                  <span>{extension.author}</span>
                </div>
                <div className="flex justify-between">
                  <span>Size:</span>
                  <span>{extension.size}</span>
                </div>
                <div className="flex justify-between">
                  <span>Updated:</span>
                  <span>{extension.lastUpdated}</span>
                </div>
                {extension.rating && (
                  <div className="flex justify-between">
                    <span>Rating:</span>
                    <span className="text-yellow-400">
                      {renderStars(extension.rating)} ({extension.rating})
                    </span>
                  </div>
                )}
                {extension.downloadCount && (
                  <div className="flex justify-between">
                    <span>Downloads:</span>
                    <span>{extension.downloadCount.toLocaleString()}</span>
                  </div>
                )}
              </div>

              <div className="flex gap-2">
                {extension.installed ? (
                  <>
                    {extension.category !== 'core' && (
                      <button
                        onClick={() => uninstallExtension(extension.id)}
                        className="flex-1 bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg transition-colors text-sm"
                      >
                        Uninstall
                      </button>
                    )}
                    <button className="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg transition-colors text-sm">
                      Settings
                    </button>
                  </>
                ) : (
                  <button
                    onClick={() => installExtension(extension.id)}
                    className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors text-sm"
                  >
                    Install
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>

        {filteredExtensions.length === 0 && (
          <div className="text-center py-12">
            <div className="text-4xl mb-4">🔍</div>
            <p className="text-gray-400">No extensions found matching your criteria</p>
          </div>
        )}

        {/* Extension Development */}
        <div className="mt-12 bg-gray-800 rounded-lg border border-gray-700 p-6">
          <h2 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
            <span>🛠️</span>
            Extension Development
          </h2>
          <p className="text-gray-400 mb-4">
            Build your own extensions for ChatLo Admin Dashboard using our comprehensive SDK.
          </p>
          <div className="flex gap-4">
            <button className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors">
              View Documentation
            </button>
            <button className="bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg transition-colors">
              Download SDK
            </button>
            <button className="bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition-colors">
              Submit Extension
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
