/**
 * Performance Monitor Page
 * Shows file processing reports with pagination
 * First page: File processing reports
 * Second page: Original performance data
 */

import React, { useState, useEffect } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { 
  faChartLine, 
  faFileAlt, 
  faClock, 
  faCheckCircle, 
  faExclamationTriangle, 
  faTimesCircle,
  faSpinner,
  faRefresh,
  faTrash,
  faChevronLeft,
  faChevronRight
} from '@fortawesome/free-solid-svg-icons'
import { fileProcessingQueue, FileProcessingTask, QueueStats } from '../services/fileProcessingQueue'
import { performanceMonitor } from '../services/performanceMonitor'

const ICONS = {
  chartLine: faChartLine,
  fileAlt: faFileAlt,
  clock: faClock,
  checkCircle: faCheckCircle,
  exclamationTriangle: faExclamationTriangle,
  timesCircle: faTimesCircle,
  spinner: faSpinner,
  refresh: faRefresh,
  trash: faTrash,
  chevronLeft: faChevronLeft,
  chevronRight: faChevronRight
}

interface PerformanceMonitorPageProps {
  onClose: () => void
}

const PerformanceMonitorPage: React.FC<PerformanceMonitorPageProps> = ({ onClose }) => {
  const [currentPage, setCurrentPage] = useState<'processing' | 'system'>('processing')
  const [stats, setStats] = useState<QueueStats | null>(null)
  const [tasks, setTasks] = useState<{
    pending: FileProcessingTask[]
    processing: FileProcessingTask[]
    completed: FileProcessingTask[]
    failed: FileProcessingTask[]
  } | null>(null)
  const [selectedTab, setSelectedTab] = useState<'all' | 'completed' | 'failed' | 'processing'>('all')
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null)

  useEffect(() => {
    loadData()
    
    // Auto-refresh every 2 seconds
    const interval = setInterval(loadData, 2000)
    setRefreshInterval(interval)

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [])

  const loadData = async () => {
    try {
      const queueStats = fileProcessingQueue.getStats()
      const allTasks = fileProcessingQueue.getAllTasks()
      
      setStats(queueStats)
      setTasks(allTasks)
    } catch (error) {
      console.error('Failed to load performance data:', error)
    }
  }

  const handleClearHistory = () => {
    fileProcessingQueue.clearHistory()
    loadData()
  }

  const formatDuration = (ms: number): string => {
    if (ms < 1000) return `${ms}ms`
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`
    return `${(ms / 60000).toFixed(1)}m`
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return `${bytes}B`
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)}KB`
    return `${(bytes / (1024 * 1024)).toFixed(1)}MB`
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return ICONS.checkCircle
      case 'failed': return ICONS.timesCircle
      case 'processing': return ICONS.spinner
      case 'pending': return ICONS.clock
      case 'cancelled': return ICONS.exclamationTriangle
      default: return ICONS.fileAlt
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-400'
      case 'failed': return 'text-red-400'
      case 'processing': return 'text-blue-400'
      case 'pending': return 'text-yellow-400'
      case 'cancelled': return 'text-gray-400'
      default: return 'text-gray-400'
    }
  }

  const getFilteredTasks = (): FileProcessingTask[] => {
    if (!tasks) return []

    switch (selectedTab) {
      case 'completed':
        return tasks.completed
      case 'failed':
        return tasks.failed
      case 'processing':
        return [...tasks.processing, ...tasks.pending]
      case 'all':
      default:
        return [
          ...tasks.processing,
          ...tasks.pending,
          ...tasks.completed.slice(-20), // Show last 20 completed
          ...tasks.failed.slice(-20)     // Show last 20 failed
        ]
    }
  }

  const renderProcessingReport = () => (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <FontAwesomeIcon icon={ICONS.fileAlt} className="text-2xl text-primary" />
          <h1 className="text-2xl font-bold text-white">File Processing Monitor</h1>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={handleClearHistory}
            className="flex items-center gap-2 px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            <FontAwesomeIcon icon={ICONS.trash} className="text-sm" />
            Clear History
          </button>
          <button
            onClick={loadData}
            className="flex items-center gap-2 px-3 py-2 bg-primary text-gray-900 rounded-lg hover:bg-primary/80 transition-colors"
          >
            <FontAwesomeIcon icon={ICONS.refresh} className="text-sm" />
            Refresh
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-neutral-800 rounded-lg p-4 border border-neutral-700">
            <div className="flex items-center gap-2 mb-2">
              <FontAwesomeIcon icon={ICONS.checkCircle} className="text-green-400" />
              <span className="text-sm text-gray-400">Completed</span>
            </div>
            <div className="text-2xl font-bold text-white">{stats.completedTasks}</div>
            <div className="text-xs text-gray-500">
              {stats.averageProcessingTime > 0 && `Avg: ${formatDuration(stats.averageProcessingTime)}`}
            </div>
          </div>

          <div className="bg-neutral-800 rounded-lg p-4 border border-neutral-700">
            <div className="flex items-center gap-2 mb-2">
              <FontAwesomeIcon icon={ICONS.timesCircle} className="text-red-400" />
              <span className="text-sm text-gray-400">Failed</span>
            </div>
            <div className="text-2xl font-bold text-white">{stats.failedTasks}</div>
            <div className="text-xs text-gray-500">
              {stats.successRate.toFixed(1)}% success rate
            </div>
          </div>

          <div className="bg-neutral-800 rounded-lg p-4 border border-neutral-700">
            <div className="flex items-center gap-2 mb-2">
              <FontAwesomeIcon icon={ICONS.spinner} className="text-blue-400" />
              <span className="text-sm text-gray-400">Processing</span>
            </div>
            <div className="text-2xl font-bold text-white">{stats.processingTasks}</div>
            <div className="text-xs text-gray-500">Active tasks</div>
          </div>

          <div className="bg-neutral-800 rounded-lg p-4 border border-neutral-700">
            <div className="flex items-center gap-2 mb-2">
              <FontAwesomeIcon icon={ICONS.clock} className="text-yellow-400" />
              <span className="text-sm text-gray-400">Pending</span>
            </div>
            <div className="text-2xl font-bold text-white">{stats.pendingTasks}</div>
            <div className="text-xs text-gray-500">In queue</div>
          </div>
        </div>
      )}

      {/* Filter Tabs */}
      <div className="flex gap-2 border-b border-neutral-700">
        {[
          { key: 'all', label: 'All Tasks', count: stats?.totalTasks || 0 },
          { key: 'completed', label: 'Completed', count: stats?.completedTasks || 0 },
          { key: 'failed', label: 'Failed', count: stats?.failedTasks || 0 },
          { key: 'processing', label: 'Active', count: (stats?.processingTasks || 0) + (stats?.pendingTasks || 0) }
        ].map(tab => (
          <button
            key={tab.key}
            onClick={() => setSelectedTab(tab.key as any)}
            className={`px-4 py-2 text-sm font-medium transition-colors ${
              selectedTab === tab.key
                ? 'text-primary border-b-2 border-primary'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            {tab.label} ({tab.count})
          </button>
        ))}
      </div>

      {/* Tasks List */}
      <div className="bg-neutral-800 rounded-lg border border-neutral-700 max-h-96 overflow-y-auto">
        <div className="p-4">
          <h3 className="text-lg font-semibold text-white mb-4">Task Details</h3>
          <div className="space-y-2">
            {getFilteredTasks().map((task, index) => (
              <div
                key={`${task.id}-${index}`}
                className="flex items-center justify-between p-3 bg-neutral-900 rounded-lg border border-neutral-600"
              >
                <div className="flex items-center gap-3 min-w-0 flex-1">
                  <FontAwesomeIcon
                    icon={getStatusIcon(task.status)}
                    className={`${getStatusColor(task.status)} ${
                      task.status === 'processing' ? 'animate-spin' : ''
                    }`}
                  />
                  <div className="min-w-0 flex-1">
                    <div className="text-sm font-medium text-white truncate">
                      {task.fileName}
                    </div>
                    <div className="text-xs text-gray-400">
                      {formatFileSize(task.fileSize)} • {task.vaultPath}
                    </div>
                    {task.error && (
                      <div className="text-xs text-red-400 mt-1 truncate">
                        Error: {task.error}
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-4 text-xs text-gray-400">
                  {task.processingTime && (
                    <span>{formatDuration(task.processingTime)}</span>
                  )}
                  {task.retryCount > 0 && (
                    <span className="text-yellow-400">
                      Retry {task.retryCount}/{task.maxRetries}
                    </span>
                  )}
                  <span className={`px-2 py-1 rounded ${getStatusColor(task.status)} bg-neutral-800`}>
                    {task.status}
                  </span>
                </div>
              </div>
            ))}
            {getFilteredTasks().length === 0 && (
              <div className="text-center py-8 text-gray-400">
                No tasks found for the selected filter.
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )

  const renderSystemPerformance = () => (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <FontAwesomeIcon icon={ICONS.chartLine} className="text-2xl text-primary" />
        <h1 className="text-2xl font-bold text-white">System Performance</h1>
      </div>
      
      <div className="bg-neutral-800 rounded-lg p-6 border border-neutral-700">
        <h3 className="text-lg font-semibold text-white mb-4">Performance Metrics</h3>
        <div className="text-gray-400">
          <p>System performance monitoring data will be displayed here.</p>
          <p className="mt-2">This includes CPU usage, memory consumption, and processing budgets.</p>
        </div>
      </div>
    </div>
  )

  return (
    <div className="fixed inset-0 bg-neutral-950 z-50 overflow-hidden">
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-neutral-700">
          <div className="flex items-center gap-4">
            <button
              onClick={onClose}
              className="p-2 hover:bg-neutral-800 rounded-lg transition-colors"
            >
              <FontAwesomeIcon icon={ICONS.chevronLeft} className="text-gray-400" />
            </button>
            <h1 className="text-xl font-bold text-white">Performance Monitor</h1>
          </div>
          
          {/* Page Navigation */}
          <div className="flex gap-2">
            <button
              onClick={() => setCurrentPage('processing')}
              className={`px-4 py-2 rounded-lg transition-colors ${
                currentPage === 'processing'
                  ? 'bg-primary text-gray-900'
                  : 'bg-neutral-800 text-gray-400 hover:text-white'
              }`}
            >
              File Processing
            </button>
            <button
              onClick={() => setCurrentPage('system')}
              className={`px-4 py-2 rounded-lg transition-colors ${
                currentPage === 'system'
                  ? 'bg-primary text-gray-900'
                  : 'bg-neutral-800 text-gray-400 hover:text-white'
              }`}
            >
              System Metrics
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {currentPage === 'processing' ? renderProcessingReport() : renderSystemPerformance()}
        </div>
      </div>
    </div>
  )
}

export default PerformanceMonitorPage
