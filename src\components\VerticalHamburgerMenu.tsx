import React, { useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { useAppStore } from '../store'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { ICONS } from './Icons/index'

interface VerticalHamburgerMenuProps {
  className?: string
}

const VerticalHamburgerMenu: React.FC<VerticalHamburgerMenuProps> = ({ className = '' }) => {
  const location = useLocation()
  const navigate = useNavigate()
  const { isPrivateMode, togglePrivateMode, sidebarOpen, setSidebarOpen } = useAppStore()
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const navigationItems = [
    {
      id: 'home',
      icon: 'fa-solid fa-home',
      label: 'Home',
      path: '/',
      isActive: location.pathname === '/'
    },
    {
      id: 'chat',
      icon: 'fa-solid fa-comment',
      label: 'Chat',
      path: '/chat',
      isActive: location.pathname.startsWith('/chat')
    },
    {
      id: 'history',
      icon: 'fa-solid fa-clock-rotate-left',
      label: 'History',
      path: '/history',
      isActive: location.pathname === '/history'
    },
    {
      id: 'files',
      icon: 'fa-solid fa-folder-tree',
      label: 'Files',
      path: '/files',
      isActive: location.pathname.startsWith('/files')
    },
    {
      id: 'settings',
      icon: 'fa-solid fa-gear',
      label: 'Settings',
      path: '/settings',
      isActive: location.pathname === '/settings'
    }
  ]

  const handleNavigation = (path: string) => {
    navigate(path)
    setIsMenuOpen(false)
  }

  const handleToggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  const handlePrivateModeToggle = () => {
    togglePrivateMode()
  }

  const handleToggleSidebar = () => {
    setSidebarOpen(!sidebarOpen)
  }

  return (
    <div className={`relative ${className}`}>
      {/* Hamburger Button */}
      <button
        onClick={handleToggleMenu}
        className="p-2 hover:bg-white/10 rounded transition-colors"
        title="Menu"
      >
        <i className="fa-solid fa-bars text-gray-300 text-sm"></i>
      </button>

      {/* Menu Overlay */}
      {isMenuOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-50"
          onClick={() => setIsMenuOpen(false)}
        />
      )}

      {/* Menu Content */}
      <div className={`
        fixed top-0 left-0 h-full w-64 bg-gray-800 border-r border-gray-700 z-50
        transform transition-transform duration-300 ease-in-out
        ${isMenuOpen ? 'translate-x-0' : '-translate-x-full'}
      `}>
        {/* Menu Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <div className="flex items-center gap-2">
            <img src="/logo_v3_bkg.png" alt="Chatlo Logo" className="w-24 h-8" />
          </div>
          <button
            onClick={() => setIsMenuOpen(false)}
            className="p-1 hover:bg-white/10 rounded transition-colors"
          >
            <FontAwesomeIcon icon={ICONS.xmark} className="text-gray-300 text-sm" />
          </button>
        </div>

        {/* Navigation Items */}
        <div className="p-4 space-y-2">
          {navigationItems.map((item) => (
            <button
              key={item.id}
              onClick={() => handleNavigation(item.path)}
              className={`
                w-full flex items-center gap-3 px-3 py-2 rounded-lg transition-colors text-left
                ${item.isActive 
                  ? 'bg-primary/20 text-primary border border-primary/30' 
                  : 'text-gray-300 hover:bg-white/10 hover:text-white'
                }
              `}
            >
              <i className={`${item.icon} text-sm w-4`}></i>
              <span className="text-sm font-medium">{item.label}</span>
            </button>
          ))}
        </div>

        {/* Controls Section */}
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-700 space-y-3">
          {/* Private Mode Toggle */}
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-300">Private Mode</span>
            <button 
              onClick={handlePrivateModeToggle}
              className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors ${
                isPrivateMode
                  ? 'bg-secondary/80'
                  : 'bg-gray-600'
              }`}
            >
              <span 
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  isPrivateMode ? 'translate-x-4' : 'translate-x-0.5'
                }`}
              />
            </button>
          </div>

          {/* Sidebar Toggle */}
          <button
            onClick={handleToggleSidebar}
            className={`
              w-full flex items-center gap-3 px-3 py-2 rounded-lg transition-colors text-left
              ${sidebarOpen 
                ? 'bg-primary/20 text-primary border border-primary/30' 
                : 'text-gray-300 hover:bg-white/10 hover:text-white'
              }
            `}
          >
            <i className="fa-solid fa-sidebar text-sm w-4"></i>
            <span className="text-sm font-medium">
              {sidebarOpen ? 'Hide Sidebar' : 'Show Sidebar'}
            </span>
          </button>
        </div>
      </div>
    </div>
  )
}

export default VerticalHamburgerMenu 