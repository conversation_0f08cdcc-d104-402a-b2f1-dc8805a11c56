/**
 * File Analysis Service
 * Specialized service for AI-powered document analysis using local models
 * Focuses on extracting 10+ key ideas with relevance scores and human connections
 */

import { BaseService, ServiceError, ServiceErrorCode } from './base'
import { localModelService } from './localModelService'
import { intelligenceStorageService } from './intelligenceStorageService'
import {
  KeyIdea,
  WeightedEntity,
  HumanConnection,
  FileIntelligence,
  FileIntelligenceConfig,
  IntentType,
  EntityType,
  DEFAULT_FILE_INTELLIGENCE_CONFIG,
  PRIORITY_WEIGHTS,
  ENTITY_PRIORITY_MAP
} from '../types/fileIntelligenceTypes'

export interface DocumentAnalysisResult {
  key_ideas: KeyIdea[]
  weighted_entities: WeightedEntity[]
  human_connections: HumanConnection[]
  processing_confidence: number
  analysis_metadata: {
    model_used: string
    processing_time_ms: number
    content_length: number
    fallback_used: boolean
  }
}

class FileAnalysisService extends BaseService {
  constructor() {
    super({
      name: 'FileAnalysisService',
      autoInitialize: true
    })
  }

  protected async doInitialize(): Promise<void> {
    this.logger.info('File Analysis Service initialized', 'doInitialize')
  }

  /**
   * Main analysis method: Extract comprehensive intelligence from document content
   */
  async analyzeDocument(
    content: string,
    config: Partial<FileIntelligenceConfig> = {}
  ): Promise<DocumentAnalysisResult> {
    const fullConfig = { ...DEFAULT_FILE_INTELLIGENCE_CONFIG, ...config }
    const startTime = Date.now()

    return await this.executeOperationOrThrow(
      'analyzeDocument',
      async () => {
        this.logger.info('Starting document analysis', 'analyzeDocument', {
          contentLength: content.length,
          model: fullConfig.local_model_preferred,
          minIdeas: fullConfig.min_ideas_required
        })

        let keyIdeas: KeyIdea[] = []
        let fallbackUsed = false
        let modelUsed = fullConfig.local_model_preferred

        try {
          // Try local model analysis first
          keyIdeas = await this.extractKeyIdeasWithLocalModel(content, fullConfig)
        } catch (error) {
          this.logger.warn('Local model analysis failed, using fallback', 'analyzeDocument', error)
          
          if (fullConfig.fallback_to_keyword_extraction) {
            keyIdeas = await this.extractKeyIdeasKeywordBased(content, fullConfig)
            fallbackUsed = true
            modelUsed = 'keyword_fallback'
          } else {
            throw error
          }
        }

        // Extract weighted entities with human connection priority
        const weightedEntities = await this.extractWeightedEntities(content, keyIdeas)

        // Extract human connections (highest priority)
        const humanConnections = await this.extractHumanConnections(content, weightedEntities)

        // Calculate overall confidence
        const processingConfidence = this.calculateOverallConfidence(keyIdeas, weightedEntities)

        const processingTime = Date.now() - startTime

        const result: DocumentAnalysisResult = {
          key_ideas: keyIdeas,
          weighted_entities: weightedEntities,
          human_connections: humanConnections,
          processing_confidence: processingConfidence,
          analysis_metadata: {
            model_used: modelUsed,
            processing_time_ms: processingTime,
            content_length: content.length,
            fallback_used: fallbackUsed
          }
        }

        this.logger.info('Document analysis completed', 'analyzeDocument', {
          ideasExtracted: keyIdeas.length,
          entitiesFound: weightedEntities.length,
          humanConnections: humanConnections.length,
          confidence: processingConfidence,
          processingTime
        })

        return result
      },
      { contentLength: content.length }
    )
  }

  /**
   * Extract key ideas using local model with enhanced prompting
   */
  private async extractKeyIdeasWithLocalModel(
    content: string,
    config: FileIntelligenceConfig
  ): Promise<KeyIdea[]> {
    // Check if local model is available
    const localModels = await localModelService.getAllLocalModels()
    const preferredModel = localModels.find(m => m.id === config.local_model_preferred)
    
    if (!preferredModel) {
      throw new ServiceError(
        ServiceErrorCode.EXTERNAL_SERVICE_ERROR,
        `Preferred local model ${config.local_model_preferred} not available`,
        { serviceName: this.serviceName, operation: 'extractKeyIdeasWithLocalModel' }
      )
    }

    // Build enhanced prompt for key idea extraction
    const prompt = this.buildEnhancedKeyIdeaPrompt(content, config.min_ideas_required)
    const messages = [{ role: 'user', content: prompt }]

    // Send to local model
    const response = await localModelService.sendMessage(
      config.local_model_preferred,
      messages
    )

    // Parse and validate response
    return this.parseAndValidateKeyIdeas(response, config)
  }

  /**
   * Build enhanced prompt for local model analysis
   */
  private buildEnhancedKeyIdeaPrompt(content: string, minIdeas: number): string {
    const truncatedContent = content.length > 8000 ? content.substring(0, 8000) + '...[truncated]' : content

    return `You are an expert document analyst. Analyze the following document and extract comprehensive key ideas with detailed metadata.

ANALYSIS REQUIREMENTS:
1. Extract at least ${minIdeas} key ideas (aim for 15-20 for comprehensive analysis)
2. Assign relevance score (0-100) based on importance and actionability
3. Classify each idea by intent types (can have multiple):
   - topic: Subject matter, themes, domain classification
   - knowledge: Technical concepts, methodologies, insights
   - connection: People, organizations, relationships, networks
   - action: Tasks, decisions, next steps, deliverables
   - reference: Citations, dependencies, related materials

4. PRIORITIZE HUMAN CONNECTIONS: Names, emails, companies, job titles
5. Focus on actionable insights and important concepts
6. Provide context for where each idea was found

DOCUMENT CONTENT:
${truncatedContent}

RESPONSE FORMAT (Valid JSON only):
{
  "key_ideas": [
    {
      "text": "Detailed description of the key idea",
      "relevance_score": 95,
      "intent_types": ["topic", "knowledge"],
      "context": "Section or context where this was found",
      "entities_mentioned": ["Person Name", "Company Inc", "<EMAIL>"]
    }
  ],
  "human_connections": [
    {
      "name": "John Smith",
      "email": "<EMAIL>",
      "company": "Company Inc",
      "title": "Senior Manager",
      "context": "Mentioned as project lead"
    }
  ]
}

Analyze the document thoroughly and extract comprehensive intelligence:`
  }

  /**
   * Parse and validate key ideas from local model response
   */
  private parseAndValidateKeyIdeas(response: string, config: FileIntelligenceConfig): KeyIdea[] {
    try {
      // Extract JSON from response
      const jsonMatch = response.match(/\{[\s\S]*\}/)
      if (!jsonMatch) {
        throw new Error('No JSON found in response')
      }

      const parsed = JSON.parse(jsonMatch[0])
      const ideas: KeyIdea[] = []

      if (parsed.key_ideas && Array.isArray(parsed.key_ideas)) {
        parsed.key_ideas.forEach((idea: any, index: number) => {
          // Validate and clean idea data
          const relevanceScore = Math.min(100, Math.max(0, idea.relevance_score || 50))
          const intentTypes = Array.isArray(idea.intent_types) ? idea.intent_types : ['topic']
          
          const keyIdea: KeyIdea = {
            id: `ai_idea_${Date.now()}_${index}`,
            text: (idea.text || '').trim(),
            relevance_score: relevanceScore,
            intent_types: intentTypes as IntentType[],
            weight: this.calculateIdeaWeight(relevanceScore, intentTypes),
            auto_selected: false, // Will be set later
            user_confirmed: false,
            context: idea.context || '',
            extracted_from: 'local_model_ai'
          }
          
          if (keyIdea.text.length > 10) { // Filter out very short ideas
            ideas.push(keyIdea)
          }
        })
      }

      // Ensure minimum ideas requirement
      if (ideas.length < config.min_ideas_required) {
        this.logger.warn('Insufficient ideas extracted from AI model', 'parseAndValidateKeyIdeas', {
          extracted: ideas.length,
          required: config.min_ideas_required
        })
      }

      // Sort by relevance score and auto-select top N
      ideas.sort((a, b) => b.relevance_score - a.relevance_score)
      ideas.slice(0, config.auto_select_top_n).forEach(idea => {
        idea.auto_selected = true
      })

      this.logger.info('Successfully parsed key ideas from AI model', 'parseAndValidateKeyIdeas', {
        totalIdeas: ideas.length,
        autoSelected: ideas.filter(i => i.auto_selected).length,
        avgRelevance: ideas.reduce((sum, i) => sum + i.relevance_score, 0) / ideas.length
      })

      return ideas

    } catch (error) {
      this.logger.error('Failed to parse AI model response', 'parseAndValidateKeyIdeas', error)
      throw new ServiceError(
        ServiceErrorCode.PROCESSING_ERROR,
        'Failed to parse key ideas from AI model response',
        { serviceName: this.serviceName, operation: 'parseAndValidateKeyIdeas', error }
      )
    }
  }

  /**
   * Calculate idea weight based on relevance score and intent types
   */
  private calculateIdeaWeight(relevanceScore: number, intentTypes: IntentType[]): number {
    let baseWeight = relevanceScore / 100
    
    // Boost weight for human connection intents
    if (intentTypes.includes('connection')) {
      baseWeight *= 1.2
    }
    
    // Boost weight for action items
    if (intentTypes.includes('action')) {
      baseWeight *= 1.1
    }
    
    return Math.min(1.0, baseWeight)
  }

  /**
   * Fallback keyword-based extraction when AI model fails
   */
  private async extractKeyIdeasKeywordBased(content: string, config: FileIntelligenceConfig): Promise<KeyIdea[]> {
    this.logger.info('Using keyword-based fallback extraction', 'extractKeyIdeasKeywordBased')
    
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 20)
    const ideas: KeyIdea[] = []

    // Enhanced keyword categories
    const keywordCategories = {
      high_priority: ['requirement', 'specification', 'design', 'architecture', 'security', 'performance'],
      medium_priority: ['feature', 'component', 'system', 'interface', 'integration', 'testing'],
      low_priority: ['data', 'user', 'deployment', 'api', 'database', 'documentation']
    }

    sentences.forEach((sentence, index) => {
      let relevanceScore = 20 // Base score
      let intentTypes: IntentType[] = ['topic']
      
      // Calculate relevance based on keyword presence
      Object.entries(keywordCategories).forEach(([priority, keywords]) => {
        const matches = keywords.filter(keyword => 
          sentence.toLowerCase().includes(keyword)
        ).length
        
        if (matches > 0) {
          switch (priority) {
            case 'high_priority':
              relevanceScore += matches * 25
              break
            case 'medium_priority':
              relevanceScore += matches * 15
              break
            case 'low_priority':
              relevanceScore += matches * 10
              break
          }
        }
      })

      // Check for human connection indicators
      if (/\b[A-Z][a-z]+ [A-Z][a-z]+\b/.test(sentence) || /@/.test(sentence)) {
        intentTypes.push('connection')
        relevanceScore += 20
      }

      if (relevanceScore > 40 && ideas.length < config.min_ideas_required * 2) {
        ideas.push({
          id: `keyword_idea_${index}`,
          text: sentence.trim(),
          relevance_score: Math.min(85, relevanceScore),
          intent_types: intentTypes,
          weight: this.calculateIdeaWeight(relevanceScore, intentTypes),
          auto_selected: ideas.length < config.auto_select_top_n,
          user_confirmed: false,
          context: 'keyword_extraction',
          extracted_from: 'keyword_fallback'
        })
      }
    })

    // Ensure minimum requirement with lower-quality ideas if needed
    while (ideas.length < config.min_ideas_required && sentences.length > ideas.length) {
      const sentence = sentences[ideas.length]
      ideas.push({
        id: `fallback_idea_${ideas.length}`,
        text: sentence.trim(),
        relevance_score: 35,
        intent_types: ['topic'],
        weight: 0.35,
        auto_selected: false,
        user_confirmed: false,
        context: 'fallback_extraction',
        extracted_from: 'keyword_fallback'
      })
    }

    return ideas.slice(0, Math.max(config.min_ideas_required, 20))
  }

  /**
   * Extract weighted entities with priority classification
   */
  private async extractWeightedEntities(content: string, keyIdeas: KeyIdea[]): Promise<WeightedEntity[]> {
    const entities: WeightedEntity[] = []

    // Extract people names (high priority)
    const namePattern = /\b[A-Z][a-z]+ [A-Z][a-z]+(?:\s[A-Z][a-z]+)?\b/g
    const names = [...new Set(content.match(namePattern) || [])]
    names.forEach(name => {
      entities.push({
        text: name,
        type: 'person',
        confidence: 0.8,
        weight: PRIORITY_WEIGHTS.high,
        intent_types: ['connection'],
        context: 'Name pattern detection',
        priority_level: 'high'
      })
    })

    // Extract email addresses (high priority)
    const emailPattern = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g
    const emails = [...new Set(content.match(emailPattern) || [])]
    emails.forEach(email => {
      entities.push({
        text: email,
        type: 'email',
        confidence: 0.95,
        weight: PRIORITY_WEIGHTS.high,
        intent_types: ['connection'],
        context: 'Email pattern detection',
        priority_level: 'high'
      })
    })

    // Extract company names (high priority)
    const companyPatterns = [
      /\b[A-Z][a-z]+ (?:Inc|Corp|LLC|Ltd|Company|Technologies|Systems|Solutions|Group|Enterprises)\b/g,
      /\b[A-Z][A-Z]+ (?:Inc|Corp|LLC|Ltd)\b/g
    ]

    const companies: string[] = []
    companyPatterns.forEach(pattern => {
      const matches = content.match(pattern) || []
      companies.push(...matches)
    })

    Array.from(new Set(companies)).forEach(company => {
      entities.push({
        text: company,
        type: 'company',
        confidence: 0.75,
        weight: PRIORITY_WEIGHTS.high,
        intent_types: ['connection'],
        context: 'Company pattern detection',
        priority_level: 'high'
      })
    })

    // Extract job titles (high priority)
    const titlePatterns = [
      /\b(?:Senior|Lead|Principal|Chief|Head of|Director of|Manager of|VP of)\s+[A-Z][a-z]+(?:\s[A-Z][a-z]+)*\b/g,
      /\b(?:CEO|CTO|CFO|COO|VP|SVP|EVP|President|Manager|Director|Engineer|Developer|Analyst|Consultant)\b/g
    ]

    const titles: string[] = []
    titlePatterns.forEach(pattern => {
      const matches = content.match(pattern) || []
      titles.push(...matches)
    })

    Array.from(new Set(titles)).forEach(title => {
      entities.push({
        text: title,
        type: 'title',
        confidence: 0.7,
        weight: PRIORITY_WEIGHTS.high,
        intent_types: ['connection'],
        context: 'Title pattern detection',
        priority_level: 'high'
      })
    })

    // Extract technical concepts (medium priority)
    const techKeywords = [
      'API', 'database', 'architecture', 'framework', 'algorithm', 'protocol',
      'interface', 'microservice', 'authentication', 'authorization', 'encryption',
      'deployment', 'integration', 'scalability', 'performance', 'security'
    ]

    techKeywords.forEach(keyword => {
      const regex = new RegExp(`\\b${keyword}\\b`, 'gi')
      const matches = content.match(regex)
      if (matches && matches.length > 0) {
        entities.push({
          text: keyword,
          type: 'technical_concept',
          confidence: 0.6,
          weight: PRIORITY_WEIGHTS.medium,
          intent_types: ['knowledge', 'topic'],
          context: `Technical keyword (${matches.length} occurrences)`,
          priority_level: 'medium'
        })
      }
    })

    return entities
  }

  /**
   * Extract human connections from entities and content
   */
  private async extractHumanConnections(content: string, entities: WeightedEntity[]): Promise<HumanConnection[]> {
    const connections: HumanConnection[] = []

    const people = entities.filter(e => e.type === 'person')
    const emails = entities.filter(e => e.type === 'email')
    const companies = entities.filter(e => e.type === 'company')
    const titles = entities.filter(e => e.type === 'title')

    people.forEach(person => {
      const connection: HumanConnection = {
        name: person.text,
        connection_strength: person.confidence,
        collaboration_context: this.extractCollaborationContext(content, person.text),
        document_mentions: this.countMentions(content, person.text),
        priority_weight: 1.0
      }

      // Try to find associated email (look for email near the person's name)
      const personIndex = content.indexOf(person.text)
      if (personIndex !== -1) {
        const contextWindow = content.substring(
          Math.max(0, personIndex - 200),
          Math.min(content.length, personIndex + 200)
        )

        const associatedEmail = emails.find(e => contextWindow.includes(e.text))
        if (associatedEmail) {
          connection.email = associatedEmail.text
        }

        // Try to find associated company
        const associatedCompany = companies.find(c => contextWindow.includes(c.text))
        if (associatedCompany) {
          connection.company = associatedCompany.text
        }

        // Try to find associated title
        const associatedTitle = titles.find(t => contextWindow.includes(t.text))
        if (associatedTitle) {
          connection.title = associatedTitle.text
        }
      }

      connections.push(connection)
    })

    return connections
  }

  /**
   * Extract collaboration context for a person
   */
  private extractCollaborationContext(content: string, personName: string): string {
    const personIndex = content.indexOf(personName)
    if (personIndex === -1) return 'Mentioned in document'

    // Extract sentence containing the person's name
    const start = Math.max(0, content.lastIndexOf('.', personIndex))
    const end = content.indexOf('.', personIndex + personName.length)

    if (end !== -1) {
      return content.substring(start + 1, end).trim()
    }

    return 'Mentioned in document'
  }

  /**
   * Count mentions of a term in content
   */
  private countMentions(content: string, term: string): number {
    const regex = new RegExp(term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi')
    const matches = content.match(regex)
    return matches ? matches.length : 0
  }

  /**
   * Calculate overall confidence score
   */
  private calculateOverallConfidence(keyIdeas: KeyIdea[], entities: WeightedEntity[]): number {
    if (keyIdeas.length === 0) return 0

    const avgIdeaScore = keyIdeas.reduce((sum, idea) => sum + idea.relevance_score, 0) / keyIdeas.length
    const avgEntityConfidence = entities.length > 0
      ? entities.reduce((sum, entity) => sum + entity.confidence, 0) / entities.length
      : 0.5

    // Weight human connections higher in confidence calculation
    const humanConnectionBonus = entities.filter(e => e.priority_level === 'high').length * 0.05

    return Math.min(1.0, (avgIdeaScore / 100 + avgEntityConfidence + humanConnectionBonus) / 2)
  }

  /**
   * Analyze document and store intelligence data
   */
  async analyzeAndStoreDocument(
    content: string,
    filePath: string,
    vaultPath: string,
    config: Partial<FileIntelligenceConfig> = {}
  ): Promise<DocumentAnalysisResult> {
    return await this.executeOperationOrThrow(
      'analyzeAndStoreDocument',
      async () => {
        this.logger.info('Analyzing and storing document', 'analyzeAndStoreDocument', { filePath })

        // Perform analysis
        const analysisResult = await this.analyzeDocument(content, config)

        // Create file intelligence object
        const fileIntelligence: FileIntelligence = {
          file_path: filePath,
          key_ideas: analysisResult.key_ideas,
          weighted_entities: analysisResult.weighted_entities,
          human_connections: analysisResult.human_connections,
          processing_confidence: analysisResult.processing_confidence,
          analysis_metadata: {
            ...analysisResult.analysis_metadata,
            timestamp: new Date().toISOString()
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }

        // Store file intelligence
        await intelligenceStorageService.storeFileIntelligence(
          filePath,
          fileIntelligence,
          { vaultPath, createDirectories: true }
        )

        // Update vault intelligence incrementally
        await intelligenceStorageService.updateVaultIntelligence(
          vaultPath,
          fileIntelligence,
          filePath
        )

        this.logger.info('Document analyzed and stored successfully', 'analyzeAndStoreDocument', {
          filePath,
          ideasExtracted: analysisResult.key_ideas.length,
          entitiesFound: analysisResult.weighted_entities.length,
          humanConnections: analysisResult.human_connections.length
        })

        // Emit performance event for monitoring
        if (typeof window !== 'undefined' && window.dispatchEvent) {
          const fileName = filePath.split('/').pop() || filePath
          window.dispatchEvent(new CustomEvent('file-intelligence-complete', {
            detail: {
              processingTime: analysisResult.analysis_metadata.processing_time_ms,
              fileName,
              ideasExtracted: analysisResult.key_ideas.length,
              entitiesFound: analysisResult.weighted_entities.length,
              humanConnections: analysisResult.human_connections.length,
              confidence: analysisResult.processing_confidence
            }
          }))
        }

        return analysisResult
      },
      { filePath, vaultPath, contentLength: content.length }
    )
  }

  /**
   * Get stored file intelligence
   */
  async getStoredFileIntelligence(filePath: string, vaultPath: string): Promise<FileIntelligence | null> {
    return await this.executeOperationOrThrow(
      'getStoredFileIntelligence',
      async () => {
        return await intelligenceStorageService.getFileIntelligence(filePath, vaultPath)
      },
      { filePath, vaultPath }
    )
  }
}

export const fileAnalysisService = new FileAnalysisService()
