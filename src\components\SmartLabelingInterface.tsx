/**
 * Smart Labeling Interface Component
 * Interactive label selection interface with AI-powered key ideas extraction
 */

import React, { useState, useEffect } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { ICONS } from './Icons/index'
import { fileAnalysisService } from '../services/fileAnalysisService'
import { 
  KeyIdea, 
  LabelSelectionState, 
  ProcessingResult,
  DEFAULT_FILE_INTELLIGENCE_CONFIG 
} from '../types/fileIntelligenceTypes'

interface SmartLabelingInterfaceProps {
  filePath: string
  fileName: string
  fileContent: string
  onLabelsChanged?: (selectedLabels: KeyIdea[]) => void
  onProcessingComplete?: (result: ProcessingResult) => void
}

export const SmartLabelingInterface: React.FC<SmartLabelingInterfaceProps> = ({
  filePath,
  fileName,
  fileContent,
  onLabelsChanged,
  onProcessingComplete
}) => {
  const [labelState, setLabelState] = useState<LabelSelectionState>({
    available_ideas: [],
    selected_idea_ids: [],
    user_annotation: '',
    processing_status: 'idle',
    show_all_ideas: false
  })

  const [isProcessing, setIsProcessing] = useState(false)
  const [processingProgress, setProcessingProgress] = useState(0)
  const [showUserInput, setShowUserInput] = useState(false)

  // Process file content when component mounts or file changes
  useEffect(() => {
    if (fileContent && filePath) {
      loadOrProcessFileIntelligence()
    }
  }, [filePath, fileContent])

  /**
   * Load existing intelligence or process file content
   */
  const loadOrProcessFileIntelligence = async () => {
    if (!fileContent || !filePath) return

    try {
      // First try to load existing intelligence data
      const vaultPath = extractVaultPath(filePath)
      if (vaultPath) {
        const existingIntelligence = await fileAnalysisService.getStoredFileIntelligence(filePath, vaultPath)

        if (existingIntelligence) {
          // Use existing intelligence data
          setLabelState(prev => ({
            ...prev,
            available_ideas: existingIntelligence.key_ideas,
            selected_idea_ids: existingIntelligence.key_ideas
              .filter((idea: any) => idea.auto_selected)
              .map((idea: any) => idea.id),
            processing_status: 'complete'
          }))

          // Notify parent components
          const selectedIdeas = existingIntelligence.key_ideas.filter((idea: any) => idea.auto_selected)
          onLabelsChanged?.(selectedIdeas)

          console.log('Loaded existing file intelligence:', {
            filePath,
            ideasCount: existingIntelligence.key_ideas.length,
            entitiesCount: existingIntelligence.weighted_entities.length
          })
          return
        }
      }

      // No existing data found, process the file
      await processFileIntelligence()
    } catch (error) {
      console.warn('Failed to load existing intelligence, processing file:', error)
      await processFileIntelligence()
    }
  }

  /**
   * Extract vault path from file path
   */
  const extractVaultPath = (filePath: string): string | null => {
    // Assuming file paths follow pattern: /vault/path/filename.ext
    // Extract vault path by removing filename
    const pathParts = filePath.split('/')
    if (pathParts.length > 1) {
      return pathParts.slice(0, -1).join('/')
    }
    return null
  }

  /**
   * Process file content with AI analysis
   */
  const processFileIntelligence = async () => {
    if (!fileContent || isProcessing) return

    setIsProcessing(true)
    setLabelState(prev => ({ ...prev, processing_status: 'processing' }))
    setProcessingProgress(0)

    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setProcessingProgress(prev => Math.min(prev + 10, 90))
      }, 200)

      // Analyze document with AI
      const analysisResult = await fileAnalysisService.analyzeDocument(fileContent, {
        ...DEFAULT_FILE_INTELLIGENCE_CONFIG,
        min_ideas_required: 12, // Extract more ideas for better selection
        auto_select_top_n: 3
      })

      clearInterval(progressInterval)
      setProcessingProgress(100)

      // Update state with extracted ideas
      setLabelState(prev => ({
        ...prev,
        available_ideas: analysisResult.key_ideas,
        selected_idea_ids: analysisResult.key_ideas
          .filter(idea => idea.auto_selected)
          .map(idea => idea.id),
        processing_status: 'complete'
      }))

      // Notify parent components
      const selectedIdeas = analysisResult.key_ideas.filter(idea => idea.auto_selected)
      onLabelsChanged?.(selectedIdeas)

      const processingResult: ProcessingResult = {
        success: true,
        file_path: filePath,
        processing_time_ms: analysisResult.analysis_metadata.processing_time_ms,
        ideas_extracted: analysisResult.key_ideas.length,
        entities_found: analysisResult.weighted_entities.length,
        human_connections: analysisResult.human_connections.length,
        confidence_score: analysisResult.processing_confidence,
        local_model_used: analysisResult.analysis_metadata.model_used
      }
      onProcessingComplete?.(processingResult)

    } catch (error) {
      console.error('File intelligence processing failed:', error)
      setLabelState(prev => ({ ...prev, processing_status: 'error' }))
      
      // Fallback to mock data for demonstration
      generateMockLabels()
    } finally {
      setIsProcessing(false)
      setTimeout(() => setProcessingProgress(0), 1000)
    }
  }

  /**
   * Generate mock labels as fallback
   */
  const generateMockLabels = () => {
    const mockIdeas: KeyIdea[] = [
      {
        id: 'mock_1',
        text: 'UI Design Specifications',
        relevance_score: 95,
        intent_types: ['topic', 'knowledge'],
        weight: 0.95,
        auto_selected: true,
        user_confirmed: false,
        context: 'Document title and main theme',
        extracted_from: 'mock_fallback'
      },
      {
        id: 'mock_2',
        text: 'Component Architecture',
        relevance_score: 88,
        intent_types: ['knowledge', 'reference'],
        weight: 0.88,
        auto_selected: true,
        user_confirmed: false,
        context: 'Technical implementation details',
        extracted_from: 'mock_fallback'
      },
      {
        id: 'mock_3',
        text: 'Requirements Analysis',
        relevance_score: 82,
        intent_types: ['topic', 'action'],
        weight: 0.82,
        auto_selected: true,
        user_confirmed: false,
        context: 'Project requirements section',
        extracted_from: 'mock_fallback'
      },
      {
        id: 'mock_4',
        text: 'Testing Protocols',
        relevance_score: 75,
        intent_types: ['action', 'knowledge'],
        weight: 0.75,
        auto_selected: false,
        user_confirmed: false,
        context: 'Quality assurance procedures',
        extracted_from: 'mock_fallback'
      },
      {
        id: 'mock_5',
        text: 'Performance Optimization',
        relevance_score: 70,
        intent_types: ['knowledge', 'action'],
        weight: 0.70,
        auto_selected: false,
        user_confirmed: false,
        context: 'System performance considerations',
        extracted_from: 'mock_fallback'
      }
    ]

    setLabelState(prev => ({
      ...prev,
      available_ideas: mockIdeas,
      selected_idea_ids: mockIdeas.filter(idea => idea.auto_selected).map(idea => idea.id),
      processing_status: 'complete'
    }))
  }

  /**
   * Toggle label selection
   */
  const toggleLabel = (ideaId: string) => {
    setLabelState(prev => {
      const isSelected = prev.selected_idea_ids.includes(ideaId)
      const newSelectedIds = isSelected
        ? prev.selected_idea_ids.filter(id => id !== ideaId)
        : [...prev.selected_idea_ids, ideaId]

      // Update user_confirmed status for the idea
      const updatedIdeas = prev.available_ideas.map(idea => 
        idea.id === ideaId 
          ? { ...idea, user_confirmed: !isSelected }
          : idea
      )

      const selectedIdeas = updatedIdeas.filter(idea => newSelectedIds.includes(idea.id))
      onLabelsChanged?.(selectedIdeas)

      return {
        ...prev,
        selected_idea_ids: newSelectedIds,
        available_ideas: updatedIdeas
      }
    })
  }

  /**
   * Add custom user annotation
   */
  const addUserAnnotation = () => {
    if (!labelState.user_annotation.trim()) return

    const customIdea: KeyIdea = {
      id: `user_${Date.now()}`,
      text: labelState.user_annotation.trim(),
      relevance_score: 90, // User annotations get high relevance
      intent_types: ['topic'],
      weight: 0.90,
      auto_selected: false,
      user_confirmed: true,
      context: 'User-provided annotation',
      extracted_from: 'user_input'
    }

    setLabelState(prev => ({
      ...prev,
      available_ideas: [customIdea, ...prev.available_ideas],
      selected_idea_ids: [customIdea.id, ...prev.selected_idea_ids],
      user_annotation: ''
    }))

    setShowUserInput(false)

    // Notify parent
    const allSelectedIdeas = [customIdea, ...labelState.available_ideas.filter(idea => 
      labelState.selected_idea_ids.includes(idea.id)
    )]
    onLabelsChanged?.(allSelectedIdeas)
  }

  /**
   * Get label color based on relevance score and selection status
   */
  const getLabelColor = (idea: KeyIdea, isSelected: boolean) => {
    if (idea.extracted_from === 'user_input') {
      return isSelected 
        ? 'bg-purple-500/20 text-purple-400 border-purple-500/30'
        : 'bg-purple-500/10 text-purple-500 border-purple-500/20'
    }

    if (idea.auto_selected && !idea.user_confirmed) {
      // Auto-selected ideas get primary colors
      if (idea.relevance_score >= 90) {
        return isSelected 
          ? 'bg-primary/20 text-primary border-primary/30'
          : 'bg-primary/10 text-primary border-primary/20'
      } else if (idea.relevance_score >= 80) {
        return isSelected 
          ? 'bg-secondary/20 text-secondary border-secondary/30'
          : 'bg-secondary/10 text-secondary border-secondary/20'
      } else {
        return isSelected 
          ? 'bg-supplement2/20 text-supplement2 border-supplement2/30'
          : 'bg-supplement2/10 text-supplement2 border-supplement2/20'
      }
    }

    // Regular ideas
    return isSelected 
      ? 'bg-gray-600 text-gray-200 border-gray-500'
      : 'bg-gray-700 text-gray-300 border-gray-600'
  }

  /**
   * Get display ideas based on show_all_ideas state
   */
  const getDisplayIdeas = () => {
    if (labelState.show_all_ideas) {
      return labelState.available_ideas
    }
    
    // Show auto-selected + top 8 ideas
    const autoSelected = labelState.available_ideas.filter(idea => idea.auto_selected)
    const others = labelState.available_ideas
      .filter(idea => !idea.auto_selected)
      .slice(0, 8 - autoSelected.length)
    
    return [...autoSelected, ...others]
  }

  return (
    <div className="p-3 border-b border-tertiary/50">
      {/* Header */}
      <div className="flex items-center justify-between mb-2">
        <p className="text-xs text-gray-400">
          Select key ideas to enhance AI context learning
        </p>
        {labelState.processing_status === 'complete' && (
          <button
            onClick={() => processFileIntelligence()}
            className="text-xs text-primary hover:text-primary/80 transition-colors"
            title="Re-analyze document"
          >
            <FontAwesomeIcon icon={ICONS.arrowsRotate} className="mr-1" />
            Refresh
          </button>
        )}
      </div>

      {/* Processing Status */}
      {isProcessing && (
        <div className="mb-3 p-2 bg-gray-900/50 rounded-lg border border-tertiary/30">
          <div className="flex items-center gap-2 mb-1">
            <FontAwesomeIcon icon={ICONS.wandMagicSparkles} className="text-primary text-sm animate-pulse" />
            <span className="text-xs text-gray-300">Analyzing document with AI...</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-1">
            <div 
              className="bg-primary h-1 rounded-full transition-all duration-300"
              style={{ width: `${processingProgress}%` }}
            />
          </div>
        </div>
      )}

      {/* Labels */}
      <div className="flex flex-wrap gap-1 mb-2">
        {getDisplayIdeas().map((idea) => {
          const isSelected = labelState.selected_idea_ids.includes(idea.id)
          return (
            <button
              key={idea.id}
              onClick={() => toggleLabel(idea.id)}
              className={`px-2 py-0.5 text-xs rounded-full border font-medium transition-all duration-200 hover:scale-105 group relative ${getLabelColor(idea, isSelected)}`}
              title={`Relevance: ${idea.relevance_score}% - ${idea.context}`}
            >
              {idea.text}
              {idea.auto_selected && !idea.user_confirmed && (
                <span className="ml-1 text-xs">⭐</span>
              )}
              
              {/* Hover tooltip */}
              <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10 pointer-events-none">
                Relevance Score: {idea.relevance_score}%
              </div>
            </button>
          )
        })}

        {/* Show More/Less Toggle */}
        {labelState.available_ideas.length > 8 && (
          <button
            onClick={() => setLabelState(prev => ({ ...prev, show_all_ideas: !prev.show_all_ideas }))}
            className="px-2 py-0.5 border border-dashed border-gray-600 text-gray-400 text-xs rounded-full hover:border-gray-500 transition-colors"
          >
            <FontAwesomeIcon icon={labelState.show_all_ideas ? ICONS.chevronUp : ICONS.chevronDown} className="mr-1" />
            {labelState.show_all_ideas ? 'Show Less' : `+${labelState.available_ideas.length - 8} More`}
          </button>
        )}

        {/* Add Custom Label */}
        <button
          onClick={() => setShowUserInput(!showUserInput)}
          className="px-2 py-0.5 border border-dashed border-gray-600 text-gray-400 text-xs rounded-full hover:border-gray-500 transition-colors"
        >
          <FontAwesomeIcon icon={ICONS.plus} className="mr-1" />
          Add
        </button>
      </div>

      {/* Custom Input */}
      {showUserInput && (
        <div className="mt-2 p-2 bg-gray-900/50 rounded-lg border border-tertiary/30">
          <div className="flex gap-2">
            <input
              type="text"
              value={labelState.user_annotation}
              onChange={(e) => setLabelState(prev => ({ ...prev, user_annotation: e.target.value }))}
              placeholder="Add custom label..."
              className="flex-1 bg-gray-800 border border-gray-600 rounded px-2 py-1 text-xs text-gray-200 placeholder-gray-500 focus:outline-none focus:border-primary/50"
              onKeyPress={(e) => e.key === 'Enter' && addUserAnnotation()}
            />
            <button
              onClick={addUserAnnotation}
              disabled={!labelState.user_annotation.trim()}
              className="px-2 py-1 bg-primary/20 text-primary border border-primary/30 rounded text-xs hover:bg-primary/30 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Add
            </button>
          </div>
        </div>
      )}

      {/* Selection Summary */}
      {labelState.selected_idea_ids.length > 0 && (
        <div className="mt-2 text-xs text-gray-400">
          {labelState.selected_idea_ids.length} label{labelState.selected_idea_ids.length !== 1 ? 's' : ''} selected
        </div>
      )}
    </div>
  )
}
