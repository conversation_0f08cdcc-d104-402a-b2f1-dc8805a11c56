import React, { useState, useEffect } from 'react'

interface PerformanceMetrics {
  cpuUsage: number
  memoryUsage: number
  diskUsage: number
  networkLatency: number
  activeConnections: number
  requestsPerSecond: number
  errorRate: number
  uptime: number
}

interface ModelPerformance {
  modelId: string
  name: string
  averageLatency: number
  throughput: number
  accuracy: number
  memoryUsage: number
  status: 'healthy' | 'degraded' | 'error'
}

export const PerformanceMonitor: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    cpuUsage: 0,
    memoryUsage: 0,
    diskUsage: 0,
    networkLatency: 0,
    activeConnections: 0,
    requestsPerSecond: 0,
    errorRate: 0,
    uptime: 0
  })
  const [modelPerformance, setModelPerformance] = useState<ModelPerformance[]>([])
  const [isMonitoring, setIsMonitoring] = useState(true)
  const [refreshInterval, setRefreshInterval] = useState(5000) // 5 seconds

  useEffect(() => {
    if (isMonitoring) {
      const interval = setInterval(updateMetrics, refreshInterval)
      return () => clearInterval(interval)
    }
  }, [isMonitoring, refreshInterval])

  const updateMetrics = () => {
    // Mock real-time data - replace with actual monitoring API
    setMetrics({
      cpuUsage: Math.random() * 100,
      memoryUsage: 60 + Math.random() * 30,
      diskUsage: 45 + Math.random() * 10,
      networkLatency: 10 + Math.random() * 50,
      activeConnections: Math.floor(Math.random() * 100),
      requestsPerSecond: Math.floor(Math.random() * 50),
      errorRate: Math.random() * 5,
      uptime: Date.now() - (24 * 60 * 60 * 1000) // 24 hours ago
    })

    // Mock model performance data
    setModelPerformance([
      {
        modelId: 'gpt-4',
        name: 'GPT-4',
        averageLatency: 1200 + Math.random() * 500,
        throughput: 15 + Math.random() * 10,
        accuracy: 0.92 + Math.random() * 0.05,
        memoryUsage: 2048 + Math.random() * 512,
        status: Math.random() > 0.1 ? 'healthy' : 'degraded'
      },
      {
        modelId: 'claude-3',
        name: 'Claude 3',
        averageLatency: 800 + Math.random() * 400,
        throughput: 20 + Math.random() * 8,
        accuracy: 0.89 + Math.random() * 0.06,
        memoryUsage: 1536 + Math.random() * 256,
        status: Math.random() > 0.15 ? 'healthy' : 'degraded'
      },
      {
        modelId: 'ollama:llama2',
        name: 'Llama 2 (Local)',
        averageLatency: 2000 + Math.random() * 1000,
        throughput: 8 + Math.random() * 5,
        accuracy: 0.85 + Math.random() * 0.08,
        memoryUsage: 4096 + Math.random() * 1024,
        status: Math.random() > 0.2 ? 'healthy' : 'error'
      }
    ])
  }

  const formatUptime = (uptime: number): string => {
    const seconds = Math.floor(uptime / 1000)
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `${hours}h ${minutes}m`
  }

  const formatBytes = (bytes: number): string => {
    if (bytes < 1024) return `${bytes} B`
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`
    if (bytes < 1024 * 1024 * 1024) return `${(bytes / (1024 * 1024)).toFixed(1)} MB`
    return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)} GB`
  }

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'healthy': return 'text-green-400'
      case 'degraded': return 'text-yellow-400'
      case 'error': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  const getStatusBg = (status: string): string => {
    switch (status) {
      case 'healthy': return 'bg-green-900/30 border-green-700'
      case 'degraded': return 'bg-yellow-900/30 border-yellow-700'
      case 'error': return 'bg-red-900/30 border-red-700'
      default: return 'bg-gray-900/30 border-gray-700'
    }
  }

  const getMetricColor = (value: number, thresholds: { warning: number; critical: number }): string => {
    if (value >= thresholds.critical) return 'text-red-400'
    if (value >= thresholds.warning) return 'text-yellow-400'
    return 'text-green-400'
  }

  return (
    <div className="h-full bg-gray-900 text-white p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">📊 Performance Monitor</h1>
              <p className="text-gray-400">Real-time system and model performance monitoring</p>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-400">Refresh:</span>
                <select
                  value={refreshInterval}
                  onChange={(e) => setRefreshInterval(Number(e.target.value))}
                  className="bg-gray-700 border border-gray-600 rounded px-3 py-1 text-white text-sm"
                >
                  <option value={1000}>1s</option>
                  <option value={5000}>5s</option>
                  <option value={10000}>10s</option>
                  <option value={30000}>30s</option>
                </select>
              </div>
              <button
                onClick={() => setIsMonitoring(!isMonitoring)}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  isMonitoring 
                    ? 'bg-red-600 hover:bg-red-700 text-white' 
                    : 'bg-green-600 hover:bg-green-700 text-white'
                }`}
              >
                {isMonitoring ? '⏸️ Pause' : '▶️ Start'}
              </button>
            </div>
          </div>
        </div>

        {/* System Metrics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-gray-400 text-sm">CPU Usage</span>
              <span className="text-lg">🖥️</span>
            </div>
            <div className={`text-2xl font-bold ${getMetricColor(metrics.cpuUsage, { warning: 70, critical: 90 })}`}>
              {metrics.cpuUsage.toFixed(1)}%
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2 mt-2">
              <div 
                className={`h-2 rounded-full transition-all duration-300 ${
                  metrics.cpuUsage >= 90 ? 'bg-red-500' : 
                  metrics.cpuUsage >= 70 ? 'bg-yellow-500' : 'bg-green-500'
                }`}
                style={{ width: `${Math.min(metrics.cpuUsage, 100)}%` }}
              />
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-gray-400 text-sm">Memory Usage</span>
              <span className="text-lg">🧠</span>
            </div>
            <div className={`text-2xl font-bold ${getMetricColor(metrics.memoryUsage, { warning: 80, critical: 95 })}`}>
              {metrics.memoryUsage.toFixed(1)}%
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2 mt-2">
              <div 
                className={`h-2 rounded-full transition-all duration-300 ${
                  metrics.memoryUsage >= 95 ? 'bg-red-500' : 
                  metrics.memoryUsage >= 80 ? 'bg-yellow-500' : 'bg-green-500'
                }`}
                style={{ width: `${Math.min(metrics.memoryUsage, 100)}%` }}
              />
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-gray-400 text-sm">Network Latency</span>
              <span className="text-lg">🌐</span>
            </div>
            <div className={`text-2xl font-bold ${getMetricColor(metrics.networkLatency, { warning: 100, critical: 200 })}`}>
              {metrics.networkLatency.toFixed(0)}ms
            </div>
            <div className="text-sm text-gray-400 mt-1">
              {metrics.activeConnections} connections
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-gray-400 text-sm">Requests/sec</span>
              <span className="text-lg">⚡</span>
            </div>
            <div className="text-2xl font-bold text-blue-400">
              {metrics.requestsPerSecond}
            </div>
            <div className="text-sm text-gray-400 mt-1">
              {metrics.errorRate.toFixed(1)}% error rate
            </div>
          </div>
        </div>

        {/* Model Performance */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6 mb-8">
          <h2 className="text-xl font-semibold text-white mb-6 flex items-center gap-2">
            <span>🤖</span>
            Model Performance
          </h2>
          <div className="space-y-4">
            {modelPerformance.map((model) => (
              <div key={model.modelId} className={`rounded-lg border p-4 ${getStatusBg(model.status)}`}>
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <span className="font-medium text-white">{model.name}</span>
                    <span className={`text-sm px-2 py-1 rounded ${getStatusColor(model.status)} bg-gray-800`}>
                      {model.status}
                    </span>
                  </div>
                  <div className="text-sm text-gray-400">
                    {model.modelId}
                  </div>
                </div>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div>
                    <div className="text-sm text-gray-400">Latency</div>
                    <div className="text-lg font-medium text-white">
                      {model.averageLatency.toFixed(0)}ms
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-400">Throughput</div>
                    <div className="text-lg font-medium text-white">
                      {model.throughput.toFixed(1)} req/s
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-400">Accuracy</div>
                    <div className="text-lg font-medium text-white">
                      {(model.accuracy * 100).toFixed(1)}%
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-400">Memory</div>
                    <div className="text-lg font-medium text-white">
                      {formatBytes(model.memoryUsage * 1024 * 1024)}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* System Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
              <span>ℹ️</span>
              System Information
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-400">Uptime:</span>
                <span className="text-white">{formatUptime(metrics.uptime)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Disk Usage:</span>
                <span className="text-white">{metrics.diskUsage.toFixed(1)}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Active Models:</span>
                <span className="text-white">{modelPerformance.filter(m => m.status === 'healthy').length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Total Requests:</span>
                <span className="text-white">12,847</span>
              </div>
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
              <span>🚨</span>
              Alerts & Warnings
            </h3>
            <div className="space-y-3">
              {metrics.cpuUsage > 90 && (
                <div className="flex items-center gap-2 text-red-400">
                  <span>⚠️</span>
                  <span className="text-sm">High CPU usage detected</span>
                </div>
              )}
              {metrics.memoryUsage > 95 && (
                <div className="flex items-center gap-2 text-red-400">
                  <span>⚠️</span>
                  <span className="text-sm">Memory usage critical</span>
                </div>
              )}
              {metrics.errorRate > 5 && (
                <div className="flex items-center gap-2 text-yellow-400">
                  <span>⚠️</span>
                  <span className="text-sm">High error rate detected</span>
                </div>
              )}
              {modelPerformance.some(m => m.status === 'error') && (
                <div className="flex items-center gap-2 text-red-400">
                  <span>⚠️</span>
                  <span className="text-sm">Model errors detected</span>
                </div>
              )}
              {modelPerformance.filter(m => m.status === 'healthy').length === modelPerformance.length && 
               metrics.cpuUsage < 70 && metrics.memoryUsage < 80 && metrics.errorRate < 2 && (
                <div className="flex items-center gap-2 text-green-400">
                  <span>✅</span>
                  <span className="text-sm">All systems operating normally</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
