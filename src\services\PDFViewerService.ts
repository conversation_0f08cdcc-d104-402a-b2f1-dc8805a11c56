/**
 * PDF Viewer Service
 * Handles PDF viewing, annotation, and text extraction
 */

export interface PDFViewerState {
  isOpen: boolean
  filePath: string | null
  fileName: string | null
  currentPage: number
  totalPages: number
  zoom: number
  isLoading: boolean
  error: string | null
  extractedText: string | null
  annotations: PDFAnnotation[]
}

export interface PDFAnnotation {
  id: string
  page: number
  x: number
  y: number
  width: number
  height: number
  text: string
  note: string
  timestamp: string
}

export interface PDFViewerOptions {
  enableAnnotations?: boolean
  enableTextExtraction?: boolean
  defaultZoom?: number
  maxZoom?: number
  minZoom?: number
}

class PDFViewerService {
  private state: PDFViewerState = {
    isOpen: false,
    filePath: null,
    fileName: null,
    currentPage: 1,
    totalPages: 0,
    zoom: 100,
    isLoading: false,
    error: null,
    extractedText: null,
    annotations: []
  }

  private listeners: Set<(state: PDFViewerState) => void> = new Set()
  private options: PDFViewerOptions = {
    enableAnnotations: true,
    enableTextExtraction: true,
    defaultZoom: 100,
    maxZoom: 300,
    minZoom: 25
  }

  constructor(options?: Partial<PDFViewerOptions>) {
    if (options) {
      this.options = { ...this.options, ...options }
    }
  }

  // Subscribe to state changes
  subscribe(listener: (state: PDFViewerState) => void): () => void {
    this.listeners.add(listener)
    return () => this.listeners.delete(listener)
  }

  // Get current state
  getState(): PDFViewerState {
    return { ...this.state }
  }

  // Notify listeners of state changes
  private notifyListeners() {
    this.listeners.forEach(listener => listener(this.getState()))
  }

  // Update state
  private updateState(updates: Partial<PDFViewerState>) {
    this.state = { ...this.state, ...updates }
    this.notifyListeners()
  }

  // Open PDF viewer
  async openPDF(filePath: string, fileName?: string): Promise<void> {
    this.updateState({
      isOpen: true,
      filePath,
      fileName: fileName || filePath.split('/').pop() || filePath,
      isLoading: true,
      error: null,
      currentPage: 1,
      zoom: this.options.defaultZoom || 100
    })

    try {
      // Load PDF metadata and content
      await this.loadPDFContent(filePath)
      
      // Extract text if enabled
      if (this.options.enableTextExtraction) {
        await this.extractPDFText(filePath)
      }
    } catch (error) {
      this.updateState({
        error: error instanceof Error ? error.message : 'Failed to load PDF',
        isLoading: false
      })
    }
  }

  // Close PDF viewer
  closePDF(): void {
    this.updateState({
      isOpen: false,
      filePath: null,
      fileName: null,
      currentPage: 1,
      totalPages: 0,
      zoom: this.options.defaultZoom || 100,
      isLoading: false,
      error: null,
      extractedText: null,
      annotations: []
    })
  }

  // Load PDF content and metadata
  private async loadPDFContent(filePath: string): Promise<void> {
    try {
      // For now, we'll use a simple approach with iframe
      // In the future, we could use pdfjs-dist for more advanced features
      this.updateState({
        isLoading: false,
        totalPages: 1 // We'll update this when we implement proper PDF parsing
      })
    } catch (error) {
      throw new Error(`Failed to load PDF: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  // Extract text from PDF
  private async extractPDFText(filePath: string): Promise<void> {
    try {
      if (window.electronAPI?.files?.processFile) {
        const result = await window.electronAPI.files.processFile(filePath, 'pdf')
        if (result.success && result.content?.text) {
          this.updateState({
            extractedText: result.content.text
          })
        }
      }
    } catch (error) {
      console.warn('Failed to extract PDF text:', error)
      // Don't throw here, text extraction is optional
    }
  }

  // Navigation methods
  goToPage(page: number): void {
    if (page >= 1 && page <= this.state.totalPages) {
      this.updateState({ currentPage: page })
    }
  }

  nextPage(): void {
    this.goToPage(this.state.currentPage + 1)
  }

  previousPage(): void {
    this.goToPage(this.state.currentPage - 1)
  }

  // Zoom methods
  setZoom(zoom: number): void {
    const minZoom = this.options.minZoom || 25
    const maxZoom = this.options.maxZoom || 300
    const clampedZoom = Math.max(minZoom, Math.min(maxZoom, zoom))
    this.updateState({ zoom: clampedZoom })
  }

  zoomIn(): void {
    this.setZoom(this.state.zoom + 25)
  }

  zoomOut(): void {
    this.setZoom(this.state.zoom - 25)
  }

  resetZoom(): void {
    this.setZoom(this.options.defaultZoom || 100)
  }

  // Annotation methods
  addAnnotation(annotation: Omit<PDFAnnotation, 'id' | 'timestamp'>): void {
    if (!this.options.enableAnnotations) return

    const newAnnotation: PDFAnnotation = {
      ...annotation,
      id: `annotation_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString()
    }

    this.updateState({
      annotations: [...this.state.annotations, newAnnotation]
    })
  }

  removeAnnotation(annotationId: string): void {
    this.updateState({
      annotations: this.state.annotations.filter(a => a.id !== annotationId)
    })
  }

  updateAnnotation(annotationId: string, updates: Partial<PDFAnnotation>): void {
    this.updateState({
      annotations: this.state.annotations.map(a => 
        a.id === annotationId ? { ...a, ...updates } : a
      )
    })
  }

  // Get annotations for current page
  getCurrentPageAnnotations(): PDFAnnotation[] {
    return this.state.annotations.filter(a => a.page === this.state.currentPage)
  }

  // Export methods
  exportAnnotations(): PDFAnnotation[] {
    return [...this.state.annotations]
  }

  importAnnotations(annotations: PDFAnnotation[]): void {
    this.updateState({ annotations: [...annotations] })
  }

  // Utility methods
  canGoNext(): boolean {
    return this.state.currentPage < this.state.totalPages
  }

  canGoPrevious(): boolean {
    return this.state.currentPage > 1
  }

  canZoomIn(): boolean {
    return this.state.zoom < (this.options.maxZoom || 300)
  }

  canZoomOut(): boolean {
    return this.state.zoom > (this.options.minZoom || 25)
  }
}

// Create singleton instance
export const pdfViewerService = new PDFViewerService()

export default PDFViewerService
